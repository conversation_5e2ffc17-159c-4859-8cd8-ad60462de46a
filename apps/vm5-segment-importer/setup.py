from setuptools import setup, find_packages

setup(
    name="vm5-segment-importer",
    version="1.0.0",
    description="VM5 受眾包資料處理工具 - 從 Cloud Storage 轉換為 BigQuery 格式",
    author="Tagtoo",
    author_email="<EMAIL>",
    packages=find_packages("src"),
    package_dir={"": "src"},
    install_requires=[
        "google-cloud-storage>=2.10.0",
        "google-cloud-bigquery>=3.11.0",
        "google-api-python-client>=2.100.0",
        "google-auth>=2.22.0",
        "pandas>=2.0.0",
        "python-dotenv>=1.0.0",
        "pydantic>=2.0.0",
        "tqdm>=4.65.0"
    ],
    python_requires=">=3.8",
    entry_points={
        "console_scripts": [
            "vm5-importer=main:main_cli",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Developers",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Information Analysis",
        "Topic :: Software Development :: Libraries :: Python Modules",
    ],
    keywords="vm5, data-processing, bigquery, google-cloud",
    project_urls={
        "Bug Reports": "https://github.com/Tagtoo/vm5-segment-importer/issues",
        "Source": "https://github.com/Tagtoo/vm5-segment-importer",
    },
)