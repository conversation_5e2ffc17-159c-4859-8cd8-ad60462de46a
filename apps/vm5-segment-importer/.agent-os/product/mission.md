# Product Mission

> Last Updated: 2025-08-06
> Version: 1.0.0

## Pitch

VM5 Segment Importer 是一個資料處理工具，幫助資料團隊自動化處理 VM5 受眾包資料，將 TSV 檔案從 Google Cloud Storage 下載並轉換為 BigQuery 格式，實現受眾包資料的標準化和媒合率計算。

## Users

### Primary Customers

- **資料工程師**: 負責資料管道維護和優化
- **資料分析師**: 需要處理受眾包資料進行分析
- **業務團隊**: 需要媒合率報告來評估合作夥伴效果

### User Personas

**資料工程師** (25-35 years old)
- **Role:** 資料工程師
- **Context:** 負責維護 Tagtoo 的資料管道，確保資料品質和處理效率
- **Pain Points:** 手動處理大量 TSV 檔案耗時費力，缺乏標準化的資料格式
- **Goals:** 自動化資料處理流程，提高資料品質，減少人工錯誤

**資料分析師** (28-40 years old)
- **Role:** 資料分析師
- **Context:** 分析受眾包效果，生成業務報告
- **Pain Points:** 資料格式不統一，缺乏媒合率資訊
- **Goals:** 獲得標準化的受眾包資料，快速生成分析報告

## The Problem

### 資料處理效率低下

目前 VM5 受眾包資料需要手動下載和處理，每個 TSV 檔案都需要人工介入。這導致處理時間長，容易出錯，且缺乏標準化。

**Our Solution:** 自動化從 Google Cloud Storage 下載 TSV 檔案，並轉換為標準化的 BigQuery 格式。

### 資料格式不統一

VM5 提供的 TSV 檔案格式與內部系統不兼容，需要複雜的轉換流程。

**Our Solution:** 建立統一的資料轉換管道，將 TSV 資料標準化為 BigQuery 表格格式。

### 缺乏媒合率資訊

無法快速了解受眾包與 Tagtoo 用戶的媒合情況，影響業務決策。

**Our Solution:** 自動計算媒合率並更新到標準化表格中。

## Differentiators

### 自動化處理流程

不同於手動處理方式，我們提供完全自動化的資料處理流程。這確保了資料處理的一致性和效率。

### 標準化資料格式

與其他資料處理工具不同，我們專門針對 VM5 受眾包資料設計，提供標準化的 BigQuery 格式輸出。

### 即時媒合率計算

相比於批次處理，我們提供即時的媒合率計算，讓業務團隊能夠快速了解受眾包效果。

## Key Features

### Core Features

- **自動化檔案下載**: 從 Google Cloud Storage 自動下載 TSV 檔案
- **資料格式轉換**: 將 TSV 格式轉換為標準化的 BigQuery 表格格式
- **媒合率計算**: 自動計算受眾包與 Tagtoo 用戶的媒合率
- **錯誤處理**: 完整的錯誤處理和日誌記錄機制
- **批次處理**: 支援批次處理多個受眾包檔案

### 監控與報告功能

- **處理狀態追蹤**: 即時追蹤檔案處理狀態
- **詳細日誌記錄**: 完整的處理日誌和錯誤報告
- **媒合率報告**: 自動生成媒合率統計報告
- **資料品質檢查**: 自動驗證資料完整性和格式正確性

### 整合功能

- **Google Cloud 整合**: 與 Google Cloud Storage 和 BigQuery 深度整合
- **Service Account 認證**: 使用專用的 Service Account 進行安全認證
- **標準化輸出**: 輸出符合 special_lta_temp_for_update_%Y%m%d 格式的資料