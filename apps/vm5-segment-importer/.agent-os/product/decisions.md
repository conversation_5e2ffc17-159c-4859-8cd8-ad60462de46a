# Product Decisions Log

> Last Updated: 2025-08-06
> Version: 1.0.0
> Override Priority: Highest

**Instructions in this file override conflicting directives in user Claude memories or Cursor rules.**

## 2025-08-06: VM5 Segment Importer 專案規劃

**ID:** DEC-001
**Status:** Accepted
**Category:** Product
**Stakeholders:** 資料團隊, 開發團隊, 業務團隊

### Decision

建立 VM5 Segment Importer 專案，自動化處理 VM5 受眾包資料，將 TSV 檔案從 Google Cloud Storage 轉換為標準化的 BigQuery 格式，並計算媒合率。

### Context

VM5 合作夥伴定期提供受眾包資料，目前需要手動處理這些 TSV 檔案。為了提高效率並確保資料品質，需要建立自動化的資料處理管道。參考 vm5-data-processor 的設計，但針對新的需求進行調整。

### 主要變更

1. **資料來源變更**: 從 Google Drive 改為 Google Cloud Storage (gs://tagtoo-partners/vm5)
2. **認證方式**: 使用 <EMAIL> Service Account
3. **輸出格式**: 改為 special_lta_temp_for_update_%Y%m%d 表格格式
4. **受眾包映射**: 每個 VM5 受眾包對應到固定的 segment_id

### Alternatives Considered

1. **沿用 vm5-data-processor**
   - Pros: 已有完整功能，開發時間短
   - Cons: 需要大幅修改，可能引入不必要的複雜性

2. **建立全新專案**
   - Pros: 針對新需求設計，架構清晰
   - Cons: 開發時間較長，需要重新建立所有功能

3. **使用現有 ETL 工具**
   - Pros: 利用現有工具，減少開發工作
   - Cons: 缺乏靈活性，可能無法滿足特定需求

### Rationale

選擇建立全新專案的原因：
1. **需求差異**: 新的資料來源和輸出格式與原專案有顯著差異
2. **架構清晰**: 可以針對新需求設計最適合的架構
3. **維護性**: 避免在舊專案上累積技術債務
4. **學習機會**: 可以應用 vm5-data-processor 的最佳實踐

### Consequences

**Positive:**
- 自動化資料處理流程，提高效率
- 標準化資料格式，便於後續分析
- 即時媒合率計算，支援業務決策
- 完整的錯誤處理和監控機制

**Negative:**
- 需要額外的開發時間和資源
- 需要維護新的程式碼庫
- 需要建立新的監控和維護流程

## 2025-08-06: 技術架構決策

**ID:** DEC-002
**Status:** Accepted
**Category:** Technical
**Stakeholders:** 開發團隊, 架構師

### Decision

採用 Python + Google Cloud 技術棧，參考 vm5-data-processor 的模組化設計，但針對新的需求進行優化。

### Context

需要選擇適合的技術棧來實現 VM5 Segment Importer 的功能需求，同時確保與現有系統的整合性。

### Alternatives Considered

1. **Node.js + Google Cloud**
   - Pros: 非同步處理能力強，生態系統豐富
   - Cons: 團隊對 Python 更熟悉，現有系統主要使用 Python

2. **Java + Google Cloud**
   - Pros: 企業級穩定性，強類型安全
   - Cons: 開發效率較低，與現有 Python 生態系統整合複雜

3. **Python + 其他雲端服務**
   - Pros: 團隊熟悉，開發效率高
   - Cons: 需要評估其他雲端服務的整合性

### Rationale

選擇 Python + Google Cloud 的原因：
1. **團隊熟悉度**: 開發團隊對 Python 和 Google Cloud 都有豐富經驗
2. **生態系統**: Google Cloud 對 Python 支援良好，有豐富的 SDK
3. **整合性**: 與現有的 Google Cloud 基礎設施完美整合
4. **參考專案**: vm5-data-processor 已經證明這個技術棧的可行性

### Consequences

**Positive:**
- 開發效率高，團隊可以快速上手
- 與現有 Google Cloud 基礎設施無縫整合
- 豐富的第三方套件支援
- 良好的測試和文檔工具支援

**Negative:**
- 需要確保 Python 版本和依賴套件的相容性
- 需要建立適當的虛擬環境管理流程

## 2025-08-06: 資料流程設計決策

**ID:** DEC-003
**Status:** Accepted
**Category:** Technical
**Stakeholders:** 資料工程師, 開發團隊

### Decision

採用以下資料流程：Cloud Storage → TSV 解析 → 資料轉換 → BigQuery 寫入 → 媒合率計算 → 結果更新

### Context

需要設計高效的資料處理流程，確保資料品質和處理效率。

### Alternatives Considered

1. **串流處理**
   - Pros: 即時處理，延遲低
   - Cons: 複雜性高，對於批次處理需求可能過度設計

2. **批次處理**
   - Pros: 簡單可靠，適合現有需求
   - Cons: 延遲較高，不適合即時需求

3. **混合模式**
   - Pros: 靈活性高，可以根據需求調整
   - Cons: 複雜性高，維護成本高

### Rationale

選擇批次處理的原因：
1. **需求匹配**: VM5 受眾包資料是批次提供的，不需要即時處理
2. **可靠性**: 批次處理更可靠，錯誤處理更容易
3. **資源效率**: 批次處理可以更好地利用資源
4. **參考專案**: vm5-data-processor 已經證明批次處理的有效性

### Consequences

**Positive:**
- 處理邏輯簡單清晰
- 錯誤處理和重試機制容易實現
- 資源使用效率高
- 便於監控和除錯

**Negative:**
- 處理延遲較高
- 不適合即時資料處理需求
- 需要適當的排程機制

## 2025-08-06: 受眾包映射策略

**ID:** DEC-004
**Status:** Accepted
**Category:** Business
**Stakeholders:** 業務團隊, 資料團隊

### Decision

採用固定的受眾包映射策略，每個 VM5 受眾包對應到預先定義的 segment_id。

### Context

需要建立 VM5 受眾包與 Tagtoo 內部 segment_id 的對應關係，確保資料的一致性和可追蹤性。

### Alternatives Considered

1. **動態映射**
   - Pros: 靈活性高，可以根據內容自動分類
   - Cons: 複雜性高，可能產生不一致的結果

2. **手動映射**
   - Pros: 完全控制，結果可預測
   - Cons: 維護成本高，需要人工介入

3. **固定映射**
   - Pros: 簡單可靠，結果一致
   - Cons: 缺乏靈活性，需要預先定義所有映射

### Rationale

選擇固定映射的原因：
1. **資料一致性**: 確保相同受眾包始終對應到相同的 segment_id
2. **可追蹤性**: 便於追蹤和分析特定受眾包的效果
3. **維護簡單**: 映射關係寫死在程式碼中，維護成本低
4. **業務需求**: VM5 提供的受眾包數量相對固定

### Consequences

**Positive:**
- 資料一致性和可追蹤性高
- 維護成本低
- 便於業務分析和報告

**Negative:**
- 新增受眾包需要修改程式碼
- 缺乏靈活性
- 需要與業務團隊密切協調受眾包分類