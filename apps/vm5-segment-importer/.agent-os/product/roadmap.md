# Product Roadmap

> Last Updated: 2025-08-06
> Version: 1.0.0
> Status: Planning

## Phase 1: 核心功能開發 (2-3 週)

**Goal:** 建立基本的檔案下載和資料轉換功能
**Success Criteria:** 能夠從 Cloud Storage 下載 TSV 檔案並轉換為 BigQuery 格式

### Must-Have Features

- [ ] Cloud Storage 下載模組 - 從 gs://tagtoo-partners/vm5 下載 TSV 檔案 `M`
- [ ] TSV 檔案驗證 - 驗證檔案格式和資料完整性 `S`
- [ ] 基本資料轉換 - 將 TSV 資料轉換為 BigQuery 格式 `M`
- [ ] BigQuery 表格建立 - 建立 special_lta_temp_for_update_%Y%m%d 表格 `S`
- [ ] 基本錯誤處理 - 處理常見的錯誤情況 `S`

### Should-Have Features

- [ ] 日誌記錄系統 - 完整的處理日誌記錄 `S`
- [ ] 配置管理 - 環境變數和設定檔管理 `S`

### Dependencies

- Google Cloud 專案設定
- Service Account 權限配置
- BigQuery 資料集建立

## Phase 2: 媒合率計算 (1-2 週)

**Goal:** 實現受眾包與 Tagtoo 用戶的媒合率計算
**Success Criteria:** 能夠計算並更新媒合率到 BigQuery 表格

### Must-Have Features

- [ ] 媒合查詢模組 - 查詢 partner_uid_mapping 表進行媒合 `M`
- [ ] 媒合率計算 - 計算每個受眾包的媒合率 `S`
- [ ] 資料更新 - 更新 BigQuery 表格中的媒合資訊 `S`
- [ ] 批次處理 - 支援批次處理多個受眾包 `M`

### Should-Have Features

- [ ] 媒合率報告 - 生成媒合率統計報告 `S`
- [ ] 效能優化 - 優化查詢和處理效能 `M`

### Dependencies

- Phase 1 完成
- partner_uid_mapping 表格存取權限

## Phase 3: 自動化與監控 (1-2 週)

**Goal:** 實現自動化處理和完整的監控功能
**Success Criteria:** 能夠自動監控新檔案並處理，提供完整的狀態追蹤

### Must-Have Features

- [ ] 檔案監控 - 監控 Cloud Storage 中的新檔案 `M`
- [ ] 自動觸發 - 新檔案自動觸發處理流程 `M`
- [ ] 狀態追蹤 - 追蹤檔案處理狀態 `S`
- [ ] 錯誤通知 - 處理失敗時發送通知 `S`

### Should-Have Features

- [ ] 重試機制 - 失敗任務的自動重試 `S`
- [ ] 效能監控 - 處理時間和資源使用監控 `S`

### Dependencies

- Phase 2 完成
- Cloud Functions 或 Cloud Run 部署

## Phase 4: 進階功能 (1-2 週)

**Goal:** 增加進階功能和優化使用者體驗
**Success Criteria:** 提供完整的資料處理解決方案

### Must-Have Features

- [ ] 受眾包映射 - 建立受眾包與 segment_id 的映射關係 `M`
- [ ] 資料品質檢查 - 自動檢查資料品質和完整性 `S`
- [ ] 歷史資料處理 - 支援處理歷史資料 `M`
- [ ] 命令列工具 - 提供完整的命令列介面 `S`

### Should-Have Features

- [ ] 資料備份 - 處理前後的資料備份 `S`
- [ ] 效能報告 - 生成處理效能報告 `S`

### Dependencies

- Phase 3 完成
- 受眾包映射配置

## Phase 5: 生產就緒 (1 週)

**Goal:** 確保系統穩定性和生產環境部署
**Success Criteria:** 系統能夠穩定運行在生產環境

### Must-Have Features

- [ ] 完整測試 - 單元測試和整合測試覆蓋率 > 80% `M`
- [ ] 文件完善 - 完整的 API 和使用文件 `S`
- [ ] 監控儀表板 - 生產環境監控儀表板 `M`
- [ ] 災難恢復 - 資料備份和恢復機制 `S`

### Should-Have Features

- [ ] 效能優化 - 最終的效能優化 `M`
- [ ] 安全審查 - 安全性檢查和加固 `S`

### Dependencies

- Phase 4 完成
- 生產環境配置

## 技術債務與維護

### 持續改進項目

- [ ] 程式碼重構 - 優化程式碼結構和可讀性
- [ ] 效能監控 - 持續監控和優化效能
- [ ] 安全性更新 - 定期更新依賴套件和安全修補
- [ ] 文件更新 - 保持文件與程式碼同步

### 維護計劃

- **每週**: 檢查錯誤日誌和效能指標
- **每月**: 更新依賴套件和安全性檢查
- **每季**: 程式碼審查和架構優化