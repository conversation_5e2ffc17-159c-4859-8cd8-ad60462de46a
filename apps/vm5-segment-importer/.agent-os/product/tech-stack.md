# Technical Stack

> Last Updated: 2025-08-06
> Version: 1.0.0

## 技術架構

### 程式語言與框架
- **Python**: 3.8+ (主要開發語言)
- **AsyncIO**: 非同步處理框架
- **Pydantic**: 資料驗證和序列化

### Google Cloud 服務
- **Google Cloud Storage**: 檔案儲存和下載
- **BigQuery**: 資料倉儲和查詢
- **Service Account**: <EMAIL>

### 資料處理
- **Pandas**: 資料處理和分析
- **Google Cloud BigQuery**: 資料庫操作
- **Google Cloud Storage**: 檔案操作

### 認證與安全
- **Google Auth**: Google Cloud 認證
- **Service Account**: 預設使用 <EMAIL>
- **IAM**: 權限管理

### 開發工具
- **pytest**: 測試框架
- **pytest-asyncio**: 非同步測試
- **python-dotenv**: 環境變數管理
- **tqdm**: 進度條顯示

### 日誌與監控
- **logging**: Python 標準日誌
- **結構化日誌**: DEBUG/INFO/WARNING/ERROR/CRITICAL

## 依賴套件

```txt
google-cloud-storage>=2.10.0
google-cloud-bigquery>=3.11.0
google-api-python-client>=2.100.0
google-auth>=2.22.0
pandas>=2.0.0
python-dotenv>=1.0.0
pytest>=7.3.1
pytest-asyncio>=0.21.0
pydantic>=2.0.0
tqdm>=4.65.0
```

## 專案結構

```
vm5-segment-importer/
├── src/
│   ├── main.py                    # 主程式入口點
│   └── vm5_importer/
│       ├── __init__.py
│       ├── storage_downloader.py  # Cloud Storage 下載
│       ├── bigquery_manager.py    # BigQuery 操作
│       ├── processor.py           # 主要處理邏輯
│       ├── tsv_validator.py      # TSV 檔案驗證
│       ├── file_parser.py        # 檔案解析
│       ├── logger.py             # 日誌管理
│       ├── exceptions.py         # 異常處理
│       └── config.py             # 配置管理
├── tests/                        # 測試檔案
├── requirements.txt              # 依賴套件
├── setup.py                     # 安裝設定
├── .env.example                 # 環境變數範例
└── README.md                    # 專案說明
```

## 資料流程

1. **輸入**: Google Cloud Storage (gs://tagtoo-partners/vm5/)
2. **處理**: TSV 檔案解析和資料轉換
3. **輸出**: BigQuery 表格 (special_lta_temp_for_update_%Y%m%d)

## 配置設定

### 環境變數
```bash
# Google Cloud 設定
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
PROJECT_ID=gothic-province-823
DATASET_ID=tagtooad

# 處理設定
STORAGE_BUCKET=tagtoo-partners
STORAGE_PREFIX=vm5
PARTNER_ID=1011
```

### BigQuery 表格結構

**special_lta_temp_for_update_%Y%m%d 表格結構:**

| Field Name | Type | Mode | Description |
|------------|------|------|-------------|
| permanent | STRING | NULLABLE | 唯一的使用者識別碼 (tagtoo_user_id) |
| segment_id | STRING | NULLABLE | 受眾標籤或區隔識別碼 |
| created_at | TIMESTAMP | NULLABLE | 受眾包生成時間 |
| source_type | STRING | NULLABLE | 資料來源類型 |
| source_entity | STRING | NULLABLE | 產生此筆資料的具體元件 |
| execution_id | STRING | NULLABLE | 該次執行的唯一識別碼 |

## 部署環境

- **開發環境**: 本地 Python 虛擬環境
- **測試環境**: Google Cloud 測試專案
- **生產環境**: Google Cloud 生產專案