# VM5 Segment Importer

> Last Updated: 2025-08-06
> Version: 1.0.0

## 專案概述

VM5 Segment Importer 是一個自動化資料處理工具，專門用於處理 VM5 合作夥伴提供的受眾包資料。本專案參考 [vm5-data-processor](https://github.com/Tagtoo/vm5-data-processor) 的設計，但針對新的需求進行了優化。

## 主要功能

### 🔄 自動化資料處理
- 從 Google Cloud Storage 自動下載 TSV 檔案
- 自動驗證檔案格式和資料完整性
- 批次處理多個受眾包檔案

### 📊 資料格式轉換
- 將 TSV 格式轉換為標準化的 BigQuery 格式
- 支援 special_lta_temp_for_update_%Y%m%d 表格結構
- 自動建立受眾包與 segment_id 的映射關係

### 🎯 媒合率計算
- 自動計算受眾包與 Tagtoo 用戶的媒合率
- 支援自定義媒合時間範圍
- 即時更新媒合結果

### 📈 監控與報告
- 完整的處理狀態追蹤
- 詳細的處理日誌和錯誤報告
- 自動生成媒合率統計報告

## 技術架構

### 核心技術
- **Python 3.8+**: 主要開發語言
- **Google Cloud Storage**: 檔案儲存和下載
- **BigQuery**: 資料倉儲和查詢
- **Service Account**: 安全認證機制

### 主要依賴
```txt
google-cloud-storage>=2.10.0
google-cloud-bigquery>=3.11.0
google-auth>=2.22.0
pandas>=2.0.0
python-dotenv>=1.0.0
pytest>=7.3.1
pydantic>=2.0.0
tqdm>=4.65.0
```

## 快速開始

### 環境設定

1. **建立虛擬環境**:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows
```

2. **安裝依賴**:
```bash
pip install -r requirements.txt
```

3. **設定環境變數**:
```bash
cp .env.example .env
# 編輯 .env 檔案，填入必要的設定
```

### 基本使用

1. **處理單一檔案**:
```bash
python src/main.py process-file gs://tagtoo-partners/vm5/example.tsv
```

2. **批次處理**:
```bash
python src/main.py process-batch
```

3. **更新媒合率**:
```bash
python src/main.py update-match-rates
```

## 配置說明

### 環境變數

| 變數名稱 | 說明 | 範例 |
|---------|------|------|
| `PROJECT_ID` | Google Cloud 專案 ID | `gothic-province-823` |
| `DATASET_ID` | BigQuery 資料集 ID | `tagtooad` |
| `STORAGE_BUCKET` | Cloud Storage 儲存桶 | `tagtoo-partners` |
| `STORAGE_PREFIX` | 檔案前綴 | `vm5` |
| `PARTNER_ID` | 合作夥伴 ID | `1011` |
| `MATCH_WINDOW_DAYS` | 媒合時間範圍 (天) | `365` |

### BigQuery 表格結構

輸出表格格式：`special_lta_temp_for_update_%Y%m%d`

| 欄位名稱 | 類型 | 說明 |
|---------|------|------|
| `permanent` | STRING | 唯一的使用者識別碼 (tagtoo_user_id) |
| `segment_id` | STRING | 受眾標籤或區隔識別碼 |
| `created_at` | TIMESTAMP | 受眾包生成時間 |
| `source_type` | STRING | 資料來源類型 |
| `source_entity` | STRING | 產生此筆資料的具體元件 |
| `execution_id` | STRING | 該次執行的唯一識別碼 |

## 受眾包映射

每個 VM5 受眾包都會對應到預先定義的 segment_id：

```python
AUDIENCE_MAPPING = {
    "增圖-實體地點-汽車相關": "vm5_auto_location",
    "增圖-網站標籤-教育學習": "vm5_edu_website",
    "BMW": "vm5_bmw",
    "LEXUS": "vm5_lexus",
    # ... 其他映射
}
```

## 開發指南

### 專案結構

```
vm5-segment-importer/
├── src/
│   ├── main.py                    # 主程式入口點
│   └── vm5_importer/
│       ├── __init__.py
│       ├── storage_downloader.py  # Cloud Storage 下載
│       ├── bigquery_manager.py    # BigQuery 操作
│       ├── processor.py           # 主要處理邏輯
│       ├── tsv_validator.py      # TSV 檔案驗證
│       ├── file_parser.py        # 檔案解析
│       ├── logger.py             # 日誌管理
│       ├── exceptions.py         # 異常處理
│       └── config.py             # 配置管理
├── tests/                        # 測試檔案
├── requirements.txt              # 依賴套件
├── setup.py                     # 安裝設定
├── .env.example                 # 環境變數範例
└── README.md                    # 專案說明
```

### 執行測試

```bash
# 執行所有測試
pytest

# 執行特定測試
pytest tests/test_processor.py

# 執行整合測試
pytest tests/integration_test.py
```

### 程式碼風格

本專案遵循 PEP8 程式碼風格規範，使用 4 空格縮排。

## 與 vm5-data-processor 的差異

| 功能 | vm5-data-processor | vm5-segment-importer |
|------|-------------------|---------------------|
| 資料來源 | Google Drive | Google Cloud Storage |
| 認證方式 | OAuth 2.0 + Service Account | Service Account 專用 |
| 輸出格式 | vm5_audiences 系列表格 | special_lta_temp_for_update_%Y%m%d |
| 受眾包映射 | 動態解析檔案名稱 | 固定映射到 segment_id |
| 媒合率計算 | 即時計算 | 批次計算 |

## 故障排除

### 常見問題

1. **認證錯誤**
   - 確認 Service Account 權限設定
   - 檢查環境變數 `GOOGLE_APPLICATION_CREDENTIALS`

2. **檔案下載失敗**
   - 確認 Cloud Storage 路徑正確
   - 檢查檔案權限設定

3. **BigQuery 操作失敗**
   - 確認專案 ID 和資料集 ID 正確
   - 檢查 BigQuery 權限設定

### 日誌查看

```bash
# 查看處理日誌
tail -f vm5_importer.log

# 查看錯誤日誌
grep "ERROR" vm5_importer.log
```

## 貢獻指南

1. Fork 本專案
2. 建立功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交變更 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 建立 Pull Request

## 授權

本專案採用 MIT 授權條款。

## 聯絡資訊

- **專案維護者**: Tagtoo 資料團隊
- **問題回報**: 請使用 GitHub Issues
- **文件更新**: 2025-08-06

## 相關連結

- [vm5-data-processor](https://github.com/Tagtoo/vm5-data-processor) - 參考專案
- [Google Cloud Storage 文件](https://cloud.google.com/storage/docs)
- [BigQuery 文件](https://cloud.google.com/bigquery/docs)