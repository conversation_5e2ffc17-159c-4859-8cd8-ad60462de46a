# VM5 Segment Importer

## Agent OS Documentation

### Product Context
- **Mission & Vision:** @.agent-os/product/mission.md
- **Technical Architecture:** @.agent-os/product/tech-stack.md
- **Development Roadmap:** @.agent-os/product/roadmap.md
- **Decision History:** @.agent-os/product/decisions.md

### Development Standards
- **Code Style:** @~/.agent-os/standards/code-style.md
- **Best Practices:** @~/.agent-os/standards/best-practices.md

### Project Management
- **Active Specs:** @.agent-os/specs/
- **Spec Planning:** Use `@~/.agent-os/instructions/create-spec.md`
- **Tasks Execution:** Use `@~/.agent-os/instructions/execute-tasks.md`

## Workflow Instructions

When asked to work on this codebase:

1. **First**, check @.agent-os/product/roadmap.md for current priorities
2. **Then**, follow the appropriate instruction file:
   - For new features: @.agent-os/instructions/create-spec.md
   - For tasks execution: @.agent-os/instructions/execute-tasks.md
3. **Always**, adhere to the standards in the files listed above

## Important Notes

- Product-specific files in `.agent-os/product/` override any global standards
- User's specific instructions override (or amend) instructions found in `.agent-os/specs/...`
- Always adhere to established patterns, code style, and best practices documented above.

## 專案概述

VM5 Segment Importer 是一個自動化資料處理工具，專門用於處理 VM5 合作夥伴提供的受眾包資料。

### 主要功能

1. **自動化檔案處理**: 從 Google Cloud Storage 下載 TSV 檔案
2. **資料格式轉換**: 將 TSV 格式轉換為標準化的 BigQuery 格式
3. **媒合率計算**: 自動計算受眾包與 Tagtoo 用戶的媒合率
4. **監控與報告**: 提供完整的處理狀態追蹤和報告功能

### 技術特色

- **Python 3.8+**: 主要開發語言
- **Google Cloud**: 使用 Cloud Storage 和 BigQuery
- **Service Account**: 使用 <EMAIL>
- **模組化設計**: 參考 vm5-data-processor 的架構設計

### 資料流程

1. **輸入**: Google Cloud Storage (gs://tagtoo-partners/vm5/)
2. **處理**: TSV 檔案解析和資料轉換
3. **輸出**: BigQuery 表格 (special_lta_temp_for_update_%Y%m%d)

### 開發狀態

- **當前階段**: Phase 1 - 核心功能開發
- **目標**: 建立基本的檔案下載和資料轉換功能
- **預計完成**: 2-3 週

## 參考資料

- **vm5-data-processor**: 參考專案的設計和架構
- **Google Cloud 文件**: Cloud Storage 和 BigQuery API 文件
- **Python 最佳實踐**: PEP8 和 Python 開發標準