# VM5 Segment Importer 設計文件

> Last Updated: 2025-08-06
> Version: 1.0.0

## 專案背景

VM5 Segment Importer 是基於 vm5-data-processor 專案設計的新一代資料處理工具。主要變更包括：

1. **資料來源**: 從 Google Drive 改為 Google Cloud Storage
2. **認證方式**: 使用專用的 Service Account
3. **輸出格式**: 改為 special_lta_temp_for_update_%Y%m%d 表格格式
4. **受眾包映射**: 固定映射到預定義的 segment_id

## 參考專案分析

### vm5-data-processor 架構優點

1. **模組化設計**: 每個功能都有獨立的模組
2. **完整的錯誤處理**: 定義了專門的異常類型
3. **詳細的日誌記錄**: 結構化的日誌系統
4. **完整的測試覆蓋**: 單元測試和整合測試
5. **靈活的配置管理**: 支援環境變數和設定檔

### 需要調整的部分

1. **資料來源模組**: 從 DriveDownloader 改為 StorageDownloader
2. **認證管理**: 簡化為 Service Account 認證
3. **輸出格式**: 調整為新的 BigQuery 表格結構
4. **媒合邏輯**: 保持原有的媒合計算邏輯

## 系統架構

### 核心模組

```
vm5_importer/
├── __init__.py
├── storage_downloader.py    # Cloud Storage 下載
├── bigquery_manager.py      # BigQuery 操作
├── processor.py             # 主要處理邏輯
├── tsv_validator.py        # TSV 檔案驗證
├── file_parser.py          # 檔案解析
├── logger.py               # 日誌管理
├── exceptions.py           # 異常處理
└── config.py              # 配置管理
```

### 資料流程

```
Cloud Storage (TSV) 
    ↓
Storage Downloader
    ↓
TSV Validator
    ↓
File Parser
    ↓
Data Processor
    ↓
BigQuery Manager
    ↓
BigQuery (special_lta_temp_for_update_%Y%m%d)
```

## 詳細設計

### 1. Storage Downloader

**功能**: 從 Google Cloud Storage 下載 TSV 檔案

**主要方法**:
- `download_file(bucket_name, blob_name, local_path)`: 下載單一檔案
- `list_files(bucket_name, prefix)`: 列出指定前綴的檔案
- `download_batch(bucket_name, prefix, local_dir)`: 批次下載

**參考 vm5-data-processor**:
- 使用 google-cloud-storage 套件
- 支援進度條顯示
- 完整的錯誤處理

### 2. TSV Validator

**功能**: 驗證 TSV 檔案格式和資料完整性

**驗證規則**:
- 檔案格式檢查
- 資料類型驗證
- 必要欄位檢查
- 資料完整性驗證

**參考 vm5-data-processor**:
- 保持原有的驗證邏輯
- 增加針對新格式的驗證

### 3. File Parser

**功能**: 解析 TSV 檔案並提取受眾包資訊

**解析邏輯**:
- 從檔案名稱提取受眾包資訊
- 建立受眾包與 segment_id 的映射
- 解析 TSV 內容為標準格式

**受眾包映射**:
```python
AUDIENCE_MAPPING = {
    "增圖-實體地點-汽車相關": "vm5_auto_location",
    "增圖-網站標籤-教育學習": "vm5_edu_website",
    # ... 其他映射
}
```

### 4. BigQuery Manager

**功能**: 管理 BigQuery 表格操作

**主要方法**:
- `create_table_if_not_exists()`: 建立表格
- `insert_data(data)`: 插入資料
- `update_match_rates()`: 更新媒合率
- `query_match_data()`: 查詢媒合資料

**表格結構**:
```sql
CREATE TABLE special_lta_temp_for_update_20250123 (
    permanent STRING,
    segment_id STRING,
    created_at TIMESTAMP,
    source_type STRING,
    source_entity STRING,
    execution_id STRING
)
```

### 5. Processor

**功能**: 主要的資料處理邏輯

**處理流程**:
1. 下載 TSV 檔案
2. 驗證檔案格式
3. 解析檔案內容
4. 轉換為標準格式
5. 寫入 BigQuery
6. 計算媒合率
7. 更新結果

**參考 vm5-data-processor**:
- 保持原有的處理流程
- 調整輸出格式
- 簡化認證流程

### 6. 異常處理

**自定義異常**:
```python
class ProcessingError(Exception):
    """處理過程中的錯誤"""
    pass

class ValidationError(Exception):
    """資料驗證錯誤"""
    pass

class BigQueryError(Exception):
    """BigQuery 操作錯誤"""
    pass
```

### 7. 日誌系統

**日誌級別**:
- DEBUG: 詳細的除錯資訊
- INFO: 一般處理資訊
- WARNING: 警告訊息
- ERROR: 錯誤訊息
- CRITICAL: 嚴重錯誤

**日誌格式**:
```python
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('vm5_importer.log'),
        logging.StreamHandler()
    ]
)
```

## 配置管理

### 環境變數

```bash
# Google Cloud 設定
GOOGLE_APPLICATION_CREDENTIALS=path/to/service-account.json
PROJECT_ID=gothic-province-823
DATASET_ID=tagtooad

# 處理設定
STORAGE_BUCKET=tagtoo-partners
STORAGE_PREFIX=vm5
PARTNER_ID=1011
MATCH_WINDOW_DAYS=365
```

### 配置類別

```python
@dataclass
class Config:
    project_id: str
    dataset_id: str
    storage_bucket: str
    storage_prefix: str
    partner_id: str
    match_window_days: int
    
    @classmethod
    def from_env(cls):
        return cls(
            project_id=os.getenv('PROJECT_ID'),
            dataset_id=os.getenv('DATASET_ID'),
            storage_bucket=os.getenv('STORAGE_BUCKET'),
            storage_prefix=os.getenv('STORAGE_PREFIX'),
            partner_id=os.getenv('PARTNER_ID'),
            match_window_days=int(os.getenv('MATCH_WINDOW_DAYS', 365))
        )
```

## 測試策略

### 單元測試

- **Storage Downloader**: 測試檔案下載功能
- **TSV Validator**: 測試檔案驗證邏輯
- **File Parser**: 測試檔案解析功能
- **BigQuery Manager**: 測試資料庫操作

### 整合測試

- **完整流程測試**: 測試從下載到寫入的完整流程
- **錯誤處理測試**: 測試各種錯誤情況的處理
- **效能測試**: 測試大量資料的處理效能

### 測試資料

- **有效 TSV 檔案**: 正常格式的測試檔案
- **無效 TSV 檔案**: 各種錯誤格式的測試檔案
- **大量資料**: 測試效能的大型檔案

## 部署策略

### 開發環境

- 本地 Python 虛擬環境
- 使用測試用的 Google Cloud 專案
- 完整的日誌和除錯功能

### 生產環境

- Google Cloud Run 或 Cloud Functions
- 使用生產用的 Google Cloud 專案
- 監控和警報機制

### 監控指標

- 檔案處理成功率
- 處理時間
- 錯誤率
- 媒合率統計

## 安全考量

### 認證安全

- 使用 Service Account 而非個人帳號
- 最小權限原則
- 定期輪換認證

### 資料安全

- 傳輸加密 (HTTPS)
- 靜態資料加密
- 存取控制

### 程式碼安全

- 依賴套件安全掃描
- 程式碼審查
- 安全最佳實踐

## 效能優化

### 批次處理

- 批次下載檔案
- 批次寫入 BigQuery
- 並行處理多個檔案

### 記憶體管理

- 串流處理大型檔案
- 適當的記憶體使用
- 垃圾回收優化

### 查詢優化

- 使用適當的 BigQuery 查詢
- 索引優化
- 查詢結果快取

## 維護計劃

### 日常維護

- 監控錯誤日誌
- 檢查處理狀態
- 更新依賴套件

### 定期維護

- 程式碼審查
- 效能評估
- 安全性檢查

### 版本更新

- 功能增強
- 錯誤修復
- 安全性更新

## 與 vm5-data-processor 的差異對比

| 功能 | vm5-data-processor | vm5-segment-importer |
|------|-------------------|---------------------|
| 資料來源 | Google Drive | Google Cloud Storage |
| 認證方式 | OAuth 2.0 + Service Account | Service Account 專用 |
| 輸出格式 | vm5_audiences 系列表格 | special_lta_temp_for_update_%Y%m%d |
| 受眾包映射 | 動態解析檔案名稱 | 固定映射到 segment_id |
| 媒合率計算 | 即時計算 | 批次計算 |
| 日誌記錄 | 本地檔案 | 結構化日誌 |
| 錯誤處理 | 基本錯誤處理 | 完整異常處理 |
| 測試覆蓋 | 單元測試 | 單元 + 整合測試 |

## 開發優先級

### Phase 1 (高優先級)
1. Storage Downloader 模組
2. TSV Validator 模組
3. 基本 BigQuery Manager
4. 核心 Processor 邏輯

### Phase 2 (中優先級)
1. 完整錯誤處理
2. 詳細日誌系統
3. 配置管理
4. 基本測試

### Phase 3 (低優先級)
1. 效能優化
2. 進階功能
3. 監控儀表板
4. 文件完善