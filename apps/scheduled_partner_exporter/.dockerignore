# Git
.git
.gitignore

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Terraform
terraform/
*.tf
*.tfstate*
.terraform/

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
joined_temp/
output/

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints/

# Documentation
README.md
*.md

# Docker
Dockerfile
.dockerignore

# Cloud Build
cloudbuild.yaml 