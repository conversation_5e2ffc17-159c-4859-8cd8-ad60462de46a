"""
BigQuery 客戶端模組
負責查詢 LTA 數據、UID 映射和費用估算
整合自子專案的 BigQuery 功能
"""

import os
import time
import logging
import threading
from typing import List, Dict, Tuple
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed

from google.cloud import bigquery
from google.cloud import storage
from google.cloud.exceptions import NotFound
from google.oauth2.service_account import Credentials

logger = logging.getLogger(__name__)


class BigQueryClient:
    """BigQuery 客戶端"""

    def __init__(self, config: Dict, config_manager=None):
        """初始化 BigQuery 客戶端

        Args:
            config: BigQuery 配置字典
        """
        # 初始化認證
        credentials_path = config.get('credentials_path')
        if credentials_path and os.path.exists(credentials_path):
            credentials = Credentials.from_service_account_file(credentials_path)
            self.client = bigquery.Client(credentials=credentials)
            self.storage = storage.Client(credentials=credentials)
        else:
            self.client = bigquery.Client()

        # 基本配置
        self.source_project = config.get('source_project', 'tagtoo-ml-workflow')
        self.source_dataset = config.get('source_dataset', 'tagtoo_export_results')
        self.mapping_project = config.get('mapping_project', 'gothic-province-823')
        self.mapping_dataset = config.get('mapping_dataset', 'tagtooad')
        self.mapping_table = config.get('mapping_table', 'partner_uid_mapping')
        self.temp_project = config.get('temp_project', 'tagtoopartners')
        self.temp_dataset = config.get('temp_dataset', 'temp_tables')
        self.max_query_cost_usd = config.get('max_query_cost_usd', 0.001)
        self.config_manager = config_manager
        self.bq_client_lock = threading.Lock()

        # LTA 表格類型配置
        self.lta_table_type = config.get('lta_table_type', 'standard')

        # 費用追蹤
        self.total_cost_usd = 0.0
        self.query_costs = []  # 記錄每次查詢的費用詳情
        self.cost_lock = threading.Lock()

        logger.info(f"BigQuery 客戶端初始化完成")
        logger.info(f"  來源專案: {self.source_project}")
        logger.info(f"  映射專案: {self.mapping_project}")
        logger.info(f"  費用上限: ${self.max_query_cost_usd}")
        logger.info(f"  LTA 表格類型: {self.lta_table_type}")

    def generate_date_range(self, start_date: str, end_date: str) -> List[str]:
        """生成日期範圍內的所有日期

        Args:
            start_date: 開始日期 (YYYYMMDD)
            end_date: 結束日期 (YYYYMMDD)

        Returns:
            日期字串列表
        """
        # 處理 YYYYMMDD 格式
        if len(start_date) == 8:
            start = datetime.strptime(start_date, '%Y%m%d')
        else:
            start = datetime.strptime(start_date, '%Y-%m-%d')

        if len(end_date) == 8:
            end = datetime.strptime(end_date, '%Y%m%d')
        else:
            end = datetime.strptime(end_date, '%Y-%m-%d')

        dates = []
        current = start
        while current <= end:
            dates.append(current.strftime('%Y%m%d'))
            current += timedelta(days=1)

        return dates

    def check_table_exists(self, project: str, dataset: str, table: str) -> bool:
        """檢查表格是否存在"""
        try:
            table_ref = f"{project}.{dataset}.{table}"
            self.client.get_table(table_ref)
            return True
        except NotFound:
            return False
        except Exception as e:
            logger.warning(f"檢查表格 {table_ref} 時發生錯誤: {str(e)}")
            return False

    def get_available_lta_tables(self, start_date: str, end_date: str) -> Tuple[List[str], List[str]]:
        """檢查指定日期範圍內可用的 LTA 表格

        Args:
            start_date: 開始日期 (YYYYMMDD)
            end_date: 結束日期 (YYYYMMDD)

        Returns:
            (可用表格列表, 缺失表格列表)
        """
        dates = self.generate_date_range(start_date, end_date)
        logger.info(f"檢查日期範圍 {start_date} 到 {end_date}，共 {len(dates)} 天的表格")
        
        available_tables = []
        missing_tables = []

        for i, date in enumerate(dates, 1):
            if self.lta_table_type == "temp_update":
                table_name = f"special_lta_temp_for_update_{date}"
            else:  # standard
                table_name = f"special_lta_{date}"

            # 每10個表格或最後一個表格時輸出進度
            if i % 10 == 0 or i == len(dates):
                logger.info(f"檢查表格進度: {i}/{len(dates)}")

            if self.check_table_exists(self.source_project, self.source_dataset, table_name):
                available_tables.append(table_name)
            else:
                missing_tables.append(table_name)

        logger.info(f"表格檢查完成: {len(available_tables)} 個可用, {len(missing_tables)} 個缺失")
        return available_tables, missing_tables

    def estimate_bytes_processed(self, query: str, query_type: str = "unknown") -> int:
        """
        對查詢執行一次「演練」(Dry Run) 來估算會處理的位元組數。

        Args:
            query: 要進行估算的 SQL 查詢字串。
            query_type: 查詢類型（用於記錄）。

        Returns:
            預計會處理的位元組數 (整數)。
        """
        try:
            logger.info(f"正在為 {query_type} 執行資料量估算 (dry run)...")
            job_config = bigquery.QueryJobConfig(dry_run=True, use_query_cache=False)
            dry_run_job = self.client.query(query, job_config=job_config)
            
            bytes_processed = dry_run_job.total_bytes_processed
            logger.info(f"預估查詢資料量: {bytes_processed / (1024**3):.2f} GB")

            return bytes_processed
        except Exception as e:
            logger.error(f"資料量估算 (Dry Run) 失敗: {e}")
            raise e

    def estimate_query_cost(self, query: str, query_type: str = "unknown") -> float:
        """估算查詢費用（美元）

        Args:
            query: SQL 查詢字串
            query_type: 查詢類型（用於記錄）

        Returns:
            預估費用 (美元)
        """
        try:
            logger.info(f"正在執行 {query_type} 的費用估算 (dry run)...")
            
            # 建立 dry run 查詢配置
            job_config = bigquery.QueryJobConfig(dry_run=True,use_query_cache=False)
            logger.info("提交 dry run 查詢到 BigQuery...")
            # 執行 dry run
            query_job = self.client.query(query, job_config=job_config)
            logger.info("Dry run 查詢完成")

            # 計算費用（每 TB $6.25）
            bytes_processed = query_job.total_bytes_processed
            cost_usd = (bytes_processed / (1024**4)) * 6.25  # 1 TB = 1024^4 bytes

            logger.info(f"預估查詢資料量: {bytes_processed / (1024**3):.2f} GB")
            logger.info(f"預估查詢費用: ${cost_usd:.6f} USD")

            return cost_usd

        except Exception as e:
            logger.error(f"費用估算失敗: {str(e)}")
            return 0.0

    def record_query_cost(self, cost_usd: float, query_type: str, description: str = ""):
        """記錄查詢費用

        Args:
            cost_usd: 查詢費用（美元）
            query_type: 查詢類型
            description: 查詢描述
        """
        with self.cost_lock:
            # 先累加費用和記錄
            self.total_cost_usd += cost_usd
            self.query_costs.append({
                'timestamp': datetime.now().isoformat(),
                'type': query_type,
                'cost_usd': cost_usd,
                'description': description
            })

            logger.info(f"記錄查詢費用: ${cost_usd:.6f} USD ({query_type})")
        logger.info(f"累計總費用: ${self.total_cost_usd:.6f} USD")

    def get_cost_summary(self) -> Dict:
        """獲取費用統計摘要

        Returns:
            費用統計字典
        """
        summary = {
            'total_cost_usd': self.total_cost_usd,
            'query_count': len(self.query_costs),
            'cost_by_type': {},
            'detailed_costs': self.query_costs
        }

        # 按查詢類型統計費用
        for cost_record in self.query_costs:
            query_type = cost_record['type']
            if query_type not in summary['cost_by_type']:
                summary['cost_by_type'][query_type] = 0.0
            summary['cost_by_type'][query_type] += cost_record['cost_usd']

        return summary

    # 這個函數是從舊的專案中複製過來的，日後增加其他partner時可以參考
    def build_mapping_query(self, partner_id: str, permanents: List[str], table_info: Dict[str, str]) -> str:
        """建構 UID 映射查詢 SQL

        Args:
            partner_id: Partner ID
            permanents: permanent ID 列表
            table_info: 表格資訊字典

        Returns:
            SQL 查詢字串
        """
        # 建立 permanents 的 SQL 條件
        permanent_conditions = [f"'{p}'" for p in permanents]
        permanent_filter = f"tagtoo_user_id IN ({', '.join(permanent_conditions)})"

        # 根據 partner 類型選擇適當的表格和欄位
        project = table_info['project']
        table = table_info['table']
        is_special = table_info.get('is_special_case', False)

        if 'feebee_id_mapping' in table:
            # Feebee 特殊案例
            query = f"""
            SELECT
                tagtoo_user_id as permanent,
                feebee_user_id as partner_user_id,
                'feebee' as mapping_type
            FROM `{project}.{table}`
            WHERE partner_id = '{partner_id}'
                AND {permanent_filter}
            """
        elif 'ttd_cookie_mapping' in table:
            # TTD 特殊案例
            query = f"""
            SELECT
                tagtoo_user_id as permanent,
                ttd_cookie_id as partner_user_id,
                'ttd' as mapping_type
            FROM `{project}.{table}`
            WHERE {permanent_filter}
            """
        else:
            # 一般的 partner_uid_mapping
            query = f"""
            SELECT
                tagtoo_user_id as permanent,
                partner_user_id,
                'standard' as mapping_type
            FROM `{project}.{table}`
            WHERE partner_id = '{partner_id}'
                AND {permanent_filter}
            """

        return query

    def _wait_for_clustering_to_complete(self, intermediate_table_id: str, first_segment_id: str, poll_interval: int = 30, timeout: int = 600) -> bool:
        """
        輪詢探測 BQ，直到叢集生效或超時。
        """
        logger.info(f"⏳ 正在等待表格 {intermediate_table_id} 的叢集生效...")
        time_waited = 0

        probe_query = f"""
        SELECT partner_user_id
        FROM `{intermediate_table_id}`
        WHERE segment_id = '{first_segment_id}'
        """

        try:
            # 取得全掃描的基準資料量
            full_scan_bytes = self.estimate_bytes_processed(
                f"SELECT segment_id FROM `{intermediate_table_id}`",
                "get_full_table_size"
            )
            if full_scan_bytes == 0: full_scan_bytes = 10 * 1024 * 1024
            logger.info(f"📊 中繼表格的全掃描大小約為: {full_scan_bytes / 1024**2:.2f} MB")

            while time_waited < timeout:
                # 探測當前的掃描量
                processed_bytes = self.estimate_bytes_processed(probe_query, "probe_clustering")
                logger.info(f"  -> [等待 {time_waited}s] 探測掃描量: {processed_bytes / 1024**2:.2f} MB")

                # 關鍵判斷
                if processed_bytes < full_scan_bytes * 0.01 or processed_bytes < 50 * 1024 * 1024:
                    logger.info("✅ 叢集已生效！")
                    return True

                time.sleep(poll_interval)
                time_waited += poll_interval
            
            logger.error(f"❌ 等待叢集生效超時 ({timeout} 秒)。")
            return False

        except Exception as e:
            logger.error(f"無法估算表格大小或探測叢集狀態: {e}")
            return False

    def _create_intermediate_table(self, segment_ids: List[str], partner_id: str, available_tables: List[str], intermediate_table_id:str) -> bool:
        """
        執行 BQ 查詢，將處理好的資料存入一個中繼表格。
        """
        logger.info("-" * 50)
        logger.info(f"🔧 正在建立中繼表格: {intermediate_table_id}")
        
        # --- 建立LTA查詢和建立中繼表語句 ---
        union_all_statements = []
        table_prefix = f"{self.source_project}.{self.source_dataset}"
        for table_name in available_tables:
            statement = f"SELECT permanent, segment_id FROM `{table_prefix}.{table_name}`"
            union_all_statements.append(statement)
        lta_source_query = " UNION ALL ".join(union_all_statements)

        formatted_segments = ", ".join([f"'{s}'" for s in segment_ids])

        create_query = f"""
        CREATE OR REPLACE TABLE `{intermediate_table_id}`
        CLUSTER BY segment_id
        AS (
            WITH
              lta_unnested_and_filtered AS (
                SELECT permanent, TRIM(segment_value) AS segment_id
                FROM ({lta_source_query})
                CROSS JOIN UNNEST(SPLIT(segment_id, ',')) AS segment_value
                WHERE TRIM(segment_value) IN ({formatted_segments})
              )
            SELECT DISTINCT a.segment_id, b.partner_user_id
            FROM lta_unnested_and_filtered AS a
            JOIN `{self.mapping_project}.{self.mapping_dataset}.{self.mapping_table}` AS b
              ON a.permanent = b.tagtoo_user_id
            WHERE b.partner_id = '{partner_id}'
              AND DATE(b.created) >= DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 30 DAY)
        );
        """
        
        # --- 執行查詢並記錄費用 ---
        logger.info("🚀 正在向 BigQuery 提交建立中繼表格的工作...")
        try:
            start_time = time.monotonic()
            estimated_cost = self.estimate_query_cost(create_query, "create_intermediate_table")
            logger.info(f"💰 預估查詢費用: {estimated_cost}")
            query_job = self.client.query(create_query)
            logger.info(f"📊 工作已提交，Job ID: {query_job.job_id}")
            query_job.result()
            end_time = time.monotonic()

            logger.info(f"✅ 中繼表格建立成功。")

            if estimated_cost > 0:
                self.record_query_cost(
                    estimated_cost,
                    "create_intermediate_table",
                    f"job_{query_job.job_id}"
                )
            return end_time - start_time
        except Exception as e:
            logger.error(f"❌ 建立中繼表格失敗: {e}")
            return 0

    def _process_one_segment_and_export(self, segment_id: str, intermediate_table_id: str, base_output_uri: str):
        """
        處理單一 segment，並直接從 BQ 匯出成 GCS 上的 CSV 檔案。
        """
        try:
            logger.info(f"  -> 🧵 開始處理 segment 並直接匯出: {segment_id}")

            # 1. 建立單純的 SELECT 查詢，專門用於「精準的」費用估算
            select_query_for_cost = f"""
            SELECT partner_user_id
            FROM `{intermediate_table_id}`
            WHERE segment_id = '{segment_id}'
            """

            # 使用 SELECT 查詢來估算真實成本
            estimated_cost = self.estimate_query_cost(select_query_for_cost, "export_single_segment")

            if estimated_cost > self.max_query_cost_usd:
                logger.error(f"預估費用 ${estimated_cost:.6f} 超過上限 ${self.max_query_cost_usd}")
                raise ValueError(f"查詢費用過高: ${estimated_cost:.6f} > ${self.max_query_cost_usd}")

            # 2. 取得中文名稱並建立 GCS 檔案路徑
            audience_name = self.config_manager.json_config.get_audience_name(segment_id)
            clean_name = audience_name.replace('/', '_').replace('\\', '_').replace(':', '_')
            gcs_output_uri = f"{base_output_uri}/{clean_name}/data-*.csv"

            # 3. 建立 EXPORT DATA 的查詢語句
            export_query = f"""
            EXPORT DATA
              OPTIONS (
                uri = '{gcs_output_uri}',
                format = 'CSV',
                overwrite = true,
                header = true,
                field_delimiter = ','
              ) AS ({select_query_for_cost});
            """

            # 4. 執行匯出工作並等待完成
            with self.bq_client_lock:
                export_job = self.client.query(export_query)
                export_job.result() # 等待這個 segment 的匯出工作完成

            if estimated_cost > 0:
                self.record_query_cost(
                    estimated_cost,
                    "export_single_segment",
                    f"segment_{segment_id}_{export_job.job_id}"
                )

            # 5. 檢查匯出的結果
            logger.info("--------------------------------------------")
            return f"✅ Segment {segment_id} ({clean_name}.csv) 成功匯出。"

        except Exception as e:
            logger.error(f"❌ 處理並匯出 Segment {segment_id} 時發生錯誤: {e}")
            return f"❌ Segment {segment_id} 匯出失敗。"

    def _process_from_intermediate_table(self, intermediate_table_id, partner_name):
        """
        結合等待與平行處理，從中繼表格拆分檔案。
        """
        # 1. 取得 segment 列表
        logger.info("-" * 50)
        logger.info(f"🔄 正在從中繼表格獲取 segment 列表...")

        query = f"SELECT DISTINCT segment_id FROM `{intermediate_table_id}`"
        segments_to_process = [row['segment_id'] for row in self.client.query(query).result()]
        logger.info(f"✅ 找到 {len(segments_to_process)} 個 segment 需要處理。")

        if not segments_to_process:
            logger.info("沒有需要處理的 segment，流程結束。")
            return None

        # 2. 等待叢集生效，確保後續查詢的成本最低
        clustering_ready = self._wait_for_clustering_to_complete(intermediate_table_id, first_segment_id=segments_to_process[0])
        if not clustering_ready:
            logger.error("因叢集未在時間內準備就緒，拆分檔案流程中止以節省費用。")
            return None

        # 3. 準備 GCS 輸出目錄的 URI 字串
        config = self.config_manager.get_user_config()
        start_dt = datetime.strptime(self.start_date, '%Y%m%d')

        # 生成 YYYY-MM 格式的資料夾名稱
        folder_timestamp = start_dt.strftime('%Y-%m')
        logger.info(f"📅 根據日期範圍 {self.start_date} 到 {self.end_date} 生成資料夾名稱: {folder_timestamp}")

        # 根據 partner 名稱設定輸出目錄，日後若增加partner可以此設定
        if partner_name == "VMFive":
            output_dir = config['gcp']['storage'].get('VMFive_export_output_path')
            base_output_uri = f"{output_dir.rstrip('/')}/{folder_timestamp}"
        else:
            logger.error(f"不支援的 partner 名稱: {partner_name}")
            return None             
        logger.info(f"📂 所有 Segment 資料夾將儲存至 GCS 基礎目錄: {base_output_uri}")

        # 4. 使用線程池平行處理所有查詢
        try:
            start_time = time.monotonic()
            MAX_WORKERS = 20
            logger.info(f"🚀 叢集已就緒！啟動 {MAX_WORKERS} 個 worker 平行匯出所有 segment...")

            with ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
                futures = {executor.submit(self._process_one_segment_and_export, seg_id, intermediate_table_id, base_output_uri): seg_id for seg_id in segments_to_process}
                
                for future in as_completed(futures):
                    result_message = future.result()
                    logger.info(result_message)

            end_time = time.monotonic()
            logger.info(" 🎉 所有檔案匯出完成！")
            return end_time - start_time
        except Exception as e:
            logger.error(f"❌ 拆分檔案流程發生錯誤: {e}")
            return None

    def get_audience_mapping_data(self,
                                segment_ids: List[str],
                                partner_id: str,
                                partner_name: str,
                                start_date: str,
                                end_date: str) -> bool:
        """
        完整執行從 BQ 查詢到 GCS 拆檔的整個流程。
        """
        # 保存日期範圍到實例變數，供後續方法使用
        self.start_date = start_date
        self.end_date = end_date
        
        cost_before = self.total_cost_usd
        overall_start_time = time.monotonic()

        # 1. 檢查可用的表格
        available_tables, missing_tables = self.get_available_lta_tables(start_date, end_date)
        logger.info(f"將檢查{available_tables}表格")
        if missing_tables:
            logger.warning(f"以下表格不存在，將跳過: {missing_tables}")
        if not available_tables:
            logger.error("沒有找到任何可用的 LTA 表格")
            return False

        # 2. 運算並建立中繼表格
        intermediate_table_id = f"{self.temp_project}.{self.temp_dataset}.{partner_id}_temp_export_table"
        step1_duration = self._create_intermediate_table(segment_ids, partner_id, available_tables, intermediate_table_id)
        if step1_duration == 0:
            logger.error("流程因建立中繼表格失敗而中止。")
            return False

        # 3. 從中繼表格拆分檔案
        step2_duration = self._process_from_intermediate_table(intermediate_table_id, partner_name)
        if step2_duration is None:
            logger.error("流程因拆分檔案失敗而中止。")
            return False

        # 4. 計算本次執行的結果
        overall_end_time = time.monotonic()
        total_duration = overall_end_time - overall_start_time
        cost_after = self.total_cost_usd
        partner_cost = cost_after - cost_before
        
        logger.info(f"✅ Partner {partner_name} 處理完成。耗時: {total_duration:.2f} 秒, 費用: ${partner_cost:.6f} USD")

        result_summary = {
            "partner_name": partner_name,
            "duration_seconds": total_duration,
            "cost_usd": partner_cost,
            "step1_duration": step1_duration,
            "step2_duration": step2_duration
        }
        return result_summary