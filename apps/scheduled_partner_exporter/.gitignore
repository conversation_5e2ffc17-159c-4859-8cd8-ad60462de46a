# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Test and coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Logs
logs/
*.log

# Local development
.env
.env.local

# Documentation
docs/_build/

# macOS
.DS_Store

# Output files
output/output_*/export/*.csv
output/output_*/mapping/*.parquet
output/output_*/stats/*.json

# Segment files
segment/**/*.csv
segment/**/*.zip
segment/**/*_backup/

# Secrets and credentials
*.json
!config/*.json
!data-partner-audience-export/config/*.json

# Keep specific files
!__init__.py