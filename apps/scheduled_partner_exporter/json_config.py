"""
JSON 配置處理器
負責處理動態名單資料 (audiences, data_partners)
支援 Google Spreadsheet 同步功能
"""

import json
import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# Google Sheets API 相關
try:
    import gspread
    from google.oauth2.service_account import Credentials
    import pandas as pd
    HAS_GSPREAD = True
except ImportError:
    HAS_GSPREAD = False

logger = logging.getLogger(__name__)


class JsonConfig:
    """JSON 配置管理器"""
    
    def __init__(self, config_dir: Path):
        """初始化 JSON 配置管理器
        
        Args:
            config_dir: 配置文件目錄
        """
        self.config_dir = Path(config_dir)
        self.data_partners_file = self.config_dir / "data_partners.json"
        
        self.data_partners = []
        self.audience_mapping = {}  # 動態受眾映射 segment_id -> name
        
        self.load_configs()
    
    def load_configs(self):
        """載入所有 JSON 配置文件"""
        self.load_data_partners()
        # 受眾映射不在初始化時載入，而是在需要時動態從 Spreadsheet 獲取
        # 但先載入預設映射作為備用
        # self._init_default_audience_mapping()
    
    def _init_default_audience_mapping(self):
        """初始化預設受眾映射（作為備用）"""
        default_audiences = self._get_default_audiences()
        for audience in default_audiences:
            segment_id = audience['segment_id']
            name = audience['name']
            self.audience_mapping[segment_id] = name
        # 
        logger.info(f"初始化預設受眾映射: {len(self.audience_mapping)} 個")
    
    def load_data_partners(self):
        """載入 Data Partners 配置"""
        try:
            if not self.data_partners_file.exists():
                logger.warning(f"Data Partners 配置文件不存在: {self.data_partners_file}")
                self.data_partners = self._get_default_data_partners()
                return
            
            with open(self.data_partners_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.data_partners = data.get('data_partners', [])
            logger.info(f"Data Partners 配置載入成功: {len(self.data_partners)} 個")
            
        except json.JSONDecodeError as e:
            logger.error(f"Data Partners 配置文件格式錯誤: {e}")
            self.data_partners = self._get_default_data_partners()
        except Exception as e:
            logger.error(f"載入 Data Partners 配置失敗: {e}")
            self.data_partners = self._get_default_data_partners()
    
    def get_audience_name(self, segment_id: str) -> str:
        """根據 segment_id 獲取受眾名稱
        
        Args:
            segment_id: segment ID
            
        Returns:
            受眾名稱，如果找不到則返回 segment_id 本身
        """
        return self.audience_mapping.get(segment_id, segment_id)
    
    def _get_default_data_partners(self) -> List[Dict]:
        """獲取預設 Data Partners 配置"""
        # 定時產受眾包之partner目前只有VMFive
        return [
            {
                "name": "VMFive",
                "id": "1011",
                "status": "RUNNING",
                "exchange_way": "Direct",
                "project": "gothic-province-823",
                "table": "tagtooad.partner_uid_mapping",
                "retention_days": 180
            }
            # {
            #     "name": "Feebee",
            #     "id": "1004", 
            #     "status": "RUNNING",
            #     "exchange_way": "Direct (Special Case)",
            #     "project": "gothic-province-823",
            #     "table": "tagtooad.feebee_id_mapping",
            #     "retention_days": 180,
            #     "special_case": True,
            #     "notes": "使用特殊的 feebee mapping 表格"
            # },
            # {
            #     "name": "The Trade Desk (TTD)",
            #     "id": "0000",
            #     "status": "STOP",
            #     "exchange_way": "Redirect",
            #     "project": "gothic-province-823",
            #     "table": "tagtooad.ttd_cookie_mapping",
            #     "retention_days": 30,
            #     "special_case": True,
            #     "notes": "使用特殊的 cookie mapping 表格"
            # }
        ]
    
    def _get_default_audiences(self) -> List[Dict]:
        """獲取預設受眾配置"""
        return [
            {
                "segment_id": "tm:d917",
                "name": "AI模型預測 - 保健與美容",
                "category": "AI模型預測",
                "enabled": True
            },
            {
                "segment_id": "tm:d1787",
                "name": "AI模型預測 - 養生美容產業VIP",
                "category": "AI模型預測",
                "enabled": True
            },
            {
                "segment_id": "tm:d1791",
                "name": "AI模型預測 - 養生美容產業高頻轉換者",
                "category": "AI模型預測",
                "enabled": True
            },
            {
                "segment_id": "tm:d1065",
                "name": "AI模型預測 - 養生美容產業購買者",
                "category": "AI模型預測",
                "enabled": True
            },
            {
                "segment_id": "tm:c_1009_3195_r_016",
                "name": "轉址網站 - 美容魔法師",
                "category": "轉址網站",
                "enabled": True
            }
        ]
    
    # Data Partners 相關方法
    def get_all_data_partners(self) -> List[Dict]:
        """獲取所有 Data Partner"""
        return self.data_partners
    
    def get_active_data_partners(self) -> List[Dict]:
        """獲取啟用的 Data Partner"""
        return [p for p in self.data_partners if p.get('status') == 'RUNNING']
    
    def get_data_partner_by_id(self, partner_id: str) -> Optional[Dict]:
        """根據 ID 獲取 Data Partner"""
        for partner in self.data_partners:
            if partner.get('id') == partner_id:
                return partner
        return None
    
    def get_partner_table_info(self, partner_id: str) -> Optional[Dict]:
        """獲取 Partner 表格資訊"""
        partner = self.get_data_partner_by_id(partner_id)
        if not partner:
            return None
        
        return {
            'project': partner.get('project'),
            'table': partner.get('table'),
            'is_special_case': partner.get('special_case', False)
        }
    
    # 受眾相關方法
    def get_audience_mapping(self) -> Dict[str, str]:
        """獲取受眾映射表 (segment_id -> name)"""
        return self.audience_mapping.copy()
    
    def has_audience_mapping(self) -> bool:
        """檢查是否已載入受眾映射"""
        return len(self.audience_mapping) > 0
    
    def validate_segment_ids(self, segment_ids: List[str]) -> Dict[str, bool]:
        """驗證 segment_id 是否存在於映射表中"""
        validation_result = {}
        
        for segment_id in segment_ids:
            validation_result[segment_id] = segment_id in self.audience_mapping
        
        return validation_result
    
    def sync_audiences_from_spreadsheet(self, 
                                      spreadsheet_id: Optional[str] = None,
                                      sheet_gid: Optional[str] = None) -> bool:
        """從 Google Spreadsheet 同步受眾配置"""
        
        if not HAS_GSPREAD:
            logger.error("缺少 Google Spreadsheet 依賴套件 (gspread)")
            return False
        
        try:
            # 使用環境變數或參數中的 spreadsheet ID
            spreadsheet_id = spreadsheet_id or os.getenv('AUDIENCE_SPREADSHEET_ID')
            sheet_gid = sheet_gid or os.getenv('AUDIENCE_SHEET_GID')
            
            if not spreadsheet_id:
                logger.error("缺少 AUDIENCE_SPREADSHEET_ID 環境變數")
                return False
            
            # 設定 Google Sheets API 認證
            # credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
            credentials_path = "tagtoo-ml-workflow-kubeflow.json"
            if not credentials_path:
                logger.error("缺少 GOOGLE_APPLICATION_CREDENTIALS 環境變數")
                return False
            
            # 讀取 Google Spreadsheet
            scope = [
                'https://www.googleapis.com/auth/spreadsheets.readonly',
                'https://www.googleapis.com/auth/drive.readonly'
            ]
            
            credentials = Credentials.from_service_account_file(
                credentials_path, scopes=scope
            )
            gc = gspread.authorize(credentials)
            
            # 開啟 spreadsheet
            sheet = gc.open_by_key(spreadsheet_id)
            
            # 如果有指定 gid，使用特定工作表
            if sheet_gid:
                worksheet = None
                for ws in sheet.worksheets():
                    if str(ws.id) == sheet_gid:
                        worksheet = ws
                        break
                if not worksheet:
                    logger.error(f"找不到 GID {sheet_gid} 的工作表")
                    return False
            else:
                worksheet = sheet.sheet1
            
            # 讀取數據 - 使用原始數據而非 records
            all_values = worksheet.get_all_values()
            
            if not all_values or len(all_values) < 2:
                logger.warning("Spreadsheet 沒有足夠的數據")
                return False
            
            # 建立 segment_id -> name 的映射 (A 欄是 segment_id，H 欄是名稱，I 欄為標記)
            audience_mapping = {}
            
            # 跳過標題行，從第二行開始處理
            for row in all_values[1:]:
                # 需有至少 9 欄（I 欄 index 8）
                if len(row) >= 9:
                    segment_id = row[0].strip() if row[0] else ''  # A 欄 (index 0)
                    name = row[7].strip() if len(row) > 7 and row[7] else ''  # H 欄 (index 7)
                    flag = row[8].strip() if len(row) > 8 and row[8] else ''  # I 欄 (index 8)
                    has_report = row[9].strip() if len(row) > 8 and row[8] else ''  # J 欄 (index 9)

                    # 找出二方且無分析報告之Segment
                    if segment_id and name and flag == 'second' and has_report != "False":
                        audience_mapping[segment_id] = name
                        logger.debug(f"映射: {segment_id} -> {name} (I欄: {flag})")
            
            if audience_mapping:
                # 更新記憶體中的映射表 (不保存到文件)
                self.audience_mapping = audience_mapping
                logger.info(f"成功同步 {len(audience_mapping)} 個受眾映射")

                # 輸出映射狀況 (供 debug)
                for segment_id, name in list(audience_mapping.items())[:5]:  # 只顯示前5個
                    logger.debug(f"  {segment_id} -> {name}")
                if len(audience_mapping) > 5:
                    logger.debug(f"  ... 及其他 {len(audience_mapping) - 5} 個")
                
                return True
            else:
                logger.warning("從 Spreadsheet 中未找到有效的受眾資料")
                logger.debug(f"Spreadsheet 總行數: {len(all_values)}")
                if all_values:
                    logger.debug(f"第一行: {all_values[0][:8] if len(all_values[0]) >= 8 else all_values[0]}")
                    if len(all_values) > 1:
                        logger.debug(f"第二行: {all_values[1][:8] if len(all_values[1]) >= 8 else all_values[1]}")
                return False
                
        except Exception as e:
            logger.error(f"同步 Spreadsheet 失敗: {str(e)}")
            import traceback
            logger.debug(f"詳細錯誤: {traceback.format_exc()}")
            return False
    
    def save_data_partners(self, data: Dict[str, Any] = None):
        """保存 Data Partners 配置"""
        if data is None:
            data = {'data_partners': self.data_partners}
        
        try:
            # 確保目錄存在
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            with open(self.data_partners_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Data Partners 配置保存成功: {self.data_partners_file}")
            
        except Exception as e:
            logger.error(f"保存 Data Partners 配置失敗: {e}")
            raise
    
    # 移除 save_audiences 方法，因為不再保存受眾到文件
    
    def validate(self) -> Dict[str, Any]:
        """驗證 JSON 配置"""
        errors = []
        
        # 檢查 Data Partners
        if not self.data_partners:
            errors.append("沒有載入任何 Data Partners")
        else:
            for i, partner in enumerate(self.data_partners):
                if not partner.get('id'):
                    errors.append(f"Data Partner {i} 缺少 ID")
                if not partner.get('name'):
                    errors.append(f"Data Partner {i} 缺少名稱")
        
        # 檢查受眾映射 (可選，因為是動態載入)
        if self.audience_mapping:
            logger.debug(f"受眾映射已載入: {len(self.audience_mapping)} 個")
        else:
            logger.debug("受眾映射未載入 (將在執行時從 Spreadsheet 同步)")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
    
    def reload(self):
        """重新載入配置"""
        self.load_configs()