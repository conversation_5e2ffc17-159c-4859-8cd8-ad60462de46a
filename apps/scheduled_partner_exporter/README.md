# Scheduled Partner Exporter

本專案自動化部署於 Google Cloud Run，並透過 Cloud Scheduler 定期排程執行。  
目前僅支援 VMFive 相關資料的自動 CSV 匯出，若需支援其他合作夥伴，請修改程式碼。

---

## 目錄結構

```
scheduled_partner_exporter/
├── main.py
├── requirements.txt
├── Dockerfile
├── .dockerignore
├── terraform/
│   ├── main.tf
│   ├── terraform.tfvars
│   ├── deploy.sh
│   └── README.md
└── ...
```

如需更細緻的內容（如 API 文件、參數說明、CI/CD 詳細流程圖等），可再補充！

---

## 功能說明

- **Cloud Run**：以 Docker 容器部署 Python 應用，支援 HTTP 觸發。
- **Cloud Scheduler**：每月5號凌晨1點自動觸發 Cloud Run 執行資料匯出。
- **自動化部署**：推送 main branch 後，GitHub Actions 會自動執行 Terraform，部署到 GCP Project `Tagtoo-Partners`。
- **Service Account**：所有操作皆以 `<EMAIL>` 服務帳戶執行，無需本地 json 憑證。
- **目前僅支援 VMFive CSV 匯出**：目前僅自動匯出 VMFive 相關資料為 CSV 檔案，若需支援其他合作夥伴，請修改 main.py 及相關邏輯。

---

## 雲端自動化流程圖

```mermaid
flowchart TD
    A[Cloud Scheduler<br>每月5號凌晨1點] -->|HTTP POST| B[Cloud Run<br>scheduled-partner-exporter]
    B --> C[main.py 執行]
    C --> D[BigQuery/Google Sheets/Storage<br>資料查詢與處理]
    D --> E[產生 VMFive 專用 CSV 檔案]
    E --> F[將結果存至指定 Storage 或寄送]
    F --> G[Log 結果]
```

---

## 程式執行流程

1. **接收 HTTP 請求**  
   Cloud Run 服務收到 Cloud Scheduler 的 HTTP POST 請求，觸發 main.py 執行。

2. **載入設定**  
   讀取 config 目錄下的設定檔，取得 BigQuery、Google Sheets、Storage 等連線資訊與匯出參數。

3. **計算日期範圍**  
   自動計算上個月的起訖日期，作為本次資料匯出的查詢範圍。

4. **同步受眾映射**  
   從 Google Sheets 取得最新的受眾對應表（如有設定），確保匯出資料的 mapping 正確。

5. **查詢資料**  
   依據設定與日期範圍，從 BigQuery 查詢 VMFive 相關的原始資料。

6. **資料處理與轉換**  
   進行資料清理、欄位轉換、格式化等處理，符合 VMFive 的 CSV 匯出規格。

7. **產生 CSV 檔案**  
   將處理後的資料輸出為 CSV 檔案，檔名通常包含日期與批次資訊。

8. **上傳至 Storage 或寄送**  
   將 CSV 檔案上傳到指定的 Google Cloud Storage bucket，或依需求寄送給 VMFive。

9. **記錄執行結果**  
   將本次執行的摘要、成功/失敗狀態、處理筆數、花費時間、錯誤訊息等寫入日誌，方便後續追蹤。

10. **回應 HTTP 請求**  
    回傳執行結果（HTTP 200/207/500），Cloud Scheduler 會記錄本次排程的狀態。

---

## 快速開始

### 1. 前置需求

- 已安裝 [gcloud CLI](https://cloud.google.com/sdk/docs/install)
- 已安裝 [Terraform](https://developer.hashicorp.com/terraform/downloads)
- 擁有 GCP `Tagtoo-Partners` 專案的操作權限
- 服務帳戶 `<EMAIL>` 具備 Cloud Run、Cloud Scheduler、BigQuery、Storage 等必要權限

### 2. 本地測試（可選）

```bash
# 啟動本地服務（需安裝 functions-framework）
pip install -r requirements.txt
export PORT=8080
python -m functions_framework --target=main --port=8080
# 另開一個 terminal 測試
curl -X POST http://localhost:8080
```

### 3. 建立 Docker 映像（本地測試用）

```bash
docker build -t scheduled-partner-exporter .
docker run -p 8080:8080 scheduled-partner-exporter
```

---

## 自動化部署（推薦）

### 1. 推送 main branch

只要將程式碼推送到 main，GitHub Actions 會自動：

- 構建 Docker 映像並推送到 GCR
- 執行 Terraform，部署/更新 Cloud Run 服務
- 建立/更新 Cloud Scheduler 排程

### 2. 手動部署（本地）

```bash
cd terraform
./deploy.sh
```

---

## Terraform 主要參數

- **project_id**: `Tagtoo-Partners`
- **service_name**: `scheduled-partner-exporter`
- **service_account_email**: `<EMAIL>`
- **region**: `asia-east1`
- **memory**: `8192` (8GB)
- **cpu**: `2`
- **timeout**: `3600` (1小時)
- **max_instances**: `10`
- **min_instances**: `0`
- **concurrency**: `10`
- **排程**: `0 1 5 * *`（每月5號凌晨1點，Asia/Taipei）

---

## 服務說明

- Cloud Run 服務網址與狀態可於 GCP Console > Cloud Run > scheduled-partner-exporter 查詢
- Cloud Scheduler 排程可於 GCP Console > Cloud Scheduler 查詢
- 日誌查詢：
  ```bash
  gcloud logging read 'resource.type=cloud_run_revision AND resource.labels.service_name=scheduled-partner-exporter' --limit=50
  ```

---

## 常見問題

- **部署後程式會自動執行嗎？**  
  只會在 Cloud Scheduler 排程或手動 HTTP 請求時執行，不會因部署自動多次執行。

- **如何手動觸發？**  
  ```bash
  curl -X POST $(terraform output -raw service_url)
  ```

- **如何修改排程？**  
  編輯 `terraform/main.tf` 的 `schedule` 參數後重新部署即可。

- **如何更換 Service Account？**  
  修改 `main.tf` 的 `service_account_email`，並確保該帳戶有足夠權限。

- **如何支援其他合作夥伴？**  
  目前僅有 VMFive 的 CSV 匯出功能，若需支援其他合作夥伴，請修改 `main.py` 及相關資料處理邏輯。

---

## 聯絡/維護

- 如需協助請聯絡專案維護者或 GCP 管理員。
- 請定期檢查 Cloud Run、Cloud Scheduler 與 GCP 日誌以確保服務正常。

---