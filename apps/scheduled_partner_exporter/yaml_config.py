"""
YAML 配置處理器
負責處理使用者的個人設定和環境配置
"""

import yaml
from pathlib import Path
from typing import Dict, Any, List
import logging

logger = logging.getLogger(__name__)


class YamlConfig:
    """YAML 配置管理器"""
    
    def __init__(self, config_path: Path):
        """初始化 YAML 配置管理器
        
        Args:
            config_path: YAML 配置文件路徑
        """
        self.config_path = Path(config_path)
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """載入 YAML 配置文件"""
        try:
            if not self.config_path.exists():
                logger.warning(f"配置文件不存在: {self.config_path}")
                self.config_data = self._get_default_config()
                return
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
            
            logger.info(f"YAML 配置載入成功: {self.config_path}")
            
        except yaml.YAMLError as e:
            logger.error(f"YAML 配置文件格式錯誤: {e}")
            self.config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"載入 YAML 配置失敗: {e}")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """獲取預設配置"""
        return {
            'gcp': {
                'auth': {
                    'credentials_path': 'tagtoo-ml-workflow-kubeflow.json'
                },
                'bigquery': {
                    'location': 'US',
                    'source_project': 'tagtoo-ml-workflow',
                    'source_dataset': 'tagtoo_export_results',
                    'mapping_project': 'gothic-province-823',
                    'mapping_dataset': 'tagtooad',
                    'max_query_cost_usd': 50.0
                },
                'storage': {
                    'project_id': None,
                    'bucket_name': None
                },
                'drive': {
                    'folder_id': '12kxs3IjP6ryA3PvBCyRT7PewzRJMTOpc'
                }
            },
            'export': {
                'output_dir': 'output',
                'compress_output': True,
                'sync_to_gcs': False,
                'sync_to_drive': True,
                'file_encoding': 'utf-8'
            },
            'analysis': {
                'start_date': '20241105',
                'end_date': '20241205',
                'default_days_back': 30
            },
            'cache': {
                'cache_dir': '.cache',
                'max_age_days': 7,
                'max_size_gb': 10.0
            },
            'logging': {
                'level': 'INFO'
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """獲取完整配置"""
        return self.config_data
    
    def get_gcp_config(self) -> Dict[str, Any]:
        """獲取 GCP 配置"""
        return self.config_data.get('gcp', {})
    
    def get_bigquery_config(self) -> Dict[str, Any]:
        """獲取 BigQuery 配置"""
        return self.get_gcp_config().get('bigquery', {})
    
    def get_storage_config(self) -> Dict[str, Any]:
        """獲取 Storage 配置"""
        return self.get_gcp_config().get('storage', {})
    
    def get_drive_config(self) -> Dict[str, Any]:
        """獲取 Drive 配置"""
        return self.get_gcp_config().get('drive', {})
    
    def get_export_config(self) -> Dict[str, Any]:
        """獲取匯出配置"""
        return self.config_data.get('export', {})
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """獲取分析配置"""
        return self.config_data.get('analysis', {})
    
    def get_cache_config(self) -> Dict[str, Any]:
        """獲取快取配置"""
        return self.config_data.get('cache', {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """獲取日誌配置"""
        return self.config_data.get('logging', {})
    
    def validate(self) -> Dict[str, Any]:
        """驗證 YAML 配置"""
        errors = []
        
        # 檢查必要的配置節點
        required_sections = ['gcp', 'export', 'analysis', 'cache']
        for section in required_sections:
            if section not in self.config_data:
                errors.append(f"缺少必要配置節點: {section}")
        
        # 檢查 GCP 配置
        gcp_config = self.get_gcp_config()
        if not gcp_config.get('auth', {}).get('credentials_path'):
            errors.append("缺少 GCP 認證文件路徑")
        
        # 檢查 BigQuery 配置
        bq_config = self.get_bigquery_config()
        required_bq_fields = ['source_project', 'source_dataset', 'mapping_project', 'mapping_dataset']
        for field in required_bq_fields:
            if not bq_config.get(field):
                errors.append(f"缺少 BigQuery 配置: {field}")
        
        # 檢查分析配置
        analysis_config = self.get_analysis_config()
        if not analysis_config.get('start_date') or not analysis_config.get('end_date'):
            errors.append("缺少分析日期範圍配置")
        
        return {
            'is_valid': len(errors) == 0,
            'errors': errors
        }
    
    def reload(self):
        """重新載入配置"""
        self.load_config()
    
    def save_config(self, config_data: Dict[str, Any] = None):
        """保存配置到文件
        
        Args:
            config_data: 要保存的配置數據，如果為 None 則保存當前配置
        """
        if config_data is not None:
            self.config_data = config_data
        
        try:
            # 確保父目錄存在
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config_data, f, default_flow_style=False, 
                         allow_unicode=True, indent=2)
            
            logger.info(f"YAML 配置保存成功: {self.config_path}")
            
        except Exception as e:
            logger.error(f"保存 YAML 配置失敗: {e}")
            raise