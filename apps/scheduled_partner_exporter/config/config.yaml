# Scheduled Parnter Exporter 統一配置文件
# 此文件包含使用者個人設定和環境配置

# GCP 服務配置
gcp:
  # 認證設定
  auth:
    credentials_path: "test"  # Service Account 金鑰路徑

  # BigQuery 設定
  bigquery:
    location: "US"  # BigQuery 區域
    source_project: "tagtoo-ml-workflow"  # LTA 數據來源專案
    source_dataset: "tagtoo_export_results"  # LTA 數據來源資料集
    mapping_project: "gothic-province-823"  # UID 映射專案
    mapping_dataset: "tagtooad"  # UID 映射資料集
    mapping_table: "partner_uid_mapping"  # UID 映射表格
    temp_project: "tagtoopartners"  # 中繼表專案
    temp_dataset: "temp_tables"  # 中繼表資料集
    max_query_cost_usd: 0.001  # 查詢費用上限 (美金) - 測試時降低限制

  # Google Cloud Storage 設定，若有其他 partner 要上傳到 GCS，可用此設定
  storage:
    project_id: null  # GCS 專案 ID，留空則不上傳到 GCS
    VMFive_export_output_path: "gs://tagtoo-partners-test/output_csv/"

  # Google Drive 設定，若之後要上傳到 Google Drive，可用此設定，但功能尚未實作
  drive:
    folder_id: "12kxs3IjP6ryA3PvBCyRT7PewzRJMTOpc"  # 上傳目標資料夾 ID

# Google Spreadsheet 同步設定
# 程式會在執行時動態從 Spreadsheet 獲取受眾對照表來映射 segment_id -> 名稱
spreadsheet:
  audience_spreadsheet_id: "1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q"
  audience_sheet_gid: "838071604"
  # 假設 A欄 是 segment_id，H欄 是受眾名稱
  segment_id_column: "A"  # segment_id 所在欄位
  name_column: "H"        # 受眾名稱所在欄位

# 匯出設定，若之後要在本地出檔，可用此設定，但功能尚未實作
export:
  output_dir: "output"  # 本地輸出目錄
  compress_output: true  # 是否生成壓縮文件
  sync_to_gcs: false  # 是否上傳到 Google Cloud Storage
  sync_to_drive: true  # 是否上傳到 Google Drive (預設)
  file_encoding: "utf-8"  # 文件編碼

# 分析設定
analysis: # 因會動態調整起訖日期，所以實際值是從main.py 中設定
  start_date: "20250501"  # 分析開始日期 (YYYYMMDD) - 回朔 30 天
  end_date: "20250616"    # 分析結束日期 (YYYYMMDD) - 今天
  default_days_back: 30   # 預設回溯天數
  lta_table_type: "temp_update"  # LTA 表格類型: "standard" 或 "temp_update"
  # standard: special_lta_YYYYMMDD (一個 permanent 對應多個 segment_ids，以 CSV 格式存儲)
  # temp_update: special_lta_temp_for_update_YYYYMMDD (一個 permanent 對應一個 segment_id，多筆記錄)

# 目前已使用google sheet 同步的受眾，此設定可略過
target_segments:
  - "tm:c_1009_3195_r_016"               # 轉址網站 - 美容魔法師
  - "tm:d917"                            # AI模型預測 - 保健與美容
  # - "tm:d1787"                           # AI模型預測 - 養生美容產業VIP
  # - "tm:d1791"                           # AI模型預測 - 養生美容產業高頻轉換者
  # - "tm:d1065"                           # AI模型預測 - 養生美容產業購買者

# 使用者選擇的目標 Data Partners (實際要處理的 partners)
# 若之後要增加其他 partner，請在此增加，並在data_partners.json中增加對應的partner 資料
target_data_partner_ids:
  - "1011"                               # VMFive

# 快取設定
cache:
  cache_dir: ".cache"     # 快取目錄
  max_age_days: 7         # 快取保存天數
  max_size_gb: 10.0       # 快取大小限制 (GB)

# 日誌設定
logging:
  level: "DEBUG"          # 日誌等級 (DEBUG, INFO, WARNING, ERROR) - 測試時使用 DEBUG

# 效能設定
performance:
  batch_size: 1000        # 批次處理大小
  max_workers: 4          # 最大並行處理數