# --- 第一階段: 編譯環境 (Builder) ---
# 使用一個包含編譯工具的完整版映像
FROM python:3.10 as builder

WORKDIR /usr/src/app

# 安裝系統依賴 (只在這個階段)
RUN apt-get update && apt-get install -y gcc g++

# 安裝 Python 依賴
# 這樣做可以利用 Docker 的快取機制，只有 requirements.txt 變動時才會重新安裝
COPY requirements.txt .
RUN pip wheel --no-cache-dir --wheel-dir /usr/src/app/wheels -r requirements.txt


# --- 第二階段: 正式環境 (Final Image) ---
# 使用一個乾淨、輕量的 slim 映像
FROM python:3.10-slim

WORKDIR /app

# 從第一階段複製已安裝好的 Python 套件
COPY --from=builder /usr/src/app/wheels /wheels
RUN pip install --no-cache-dir /wheels/*

# 複製您的應用程式代碼
COPY . .

# 設定環境變數 (可選，Gunicorn 會自動處理 PORT)
ENV PYTHONPATH=/app

# 【修改】使用 Gunicorn 啟動您的應用
# --timeout 3600 讓請求最長可執行 1 小時，對應 Cloud Run 的最大超時
# :$PORT 會自動讀取 Cloud Run 提供的端口環境變數
CMD ["gunicorn", "--bind", ":$PORT", "--workers", "1", "--threads", "8", "--timeout", "3600", "main:main"]