#!/usr/bin/env python3
"""
主要執行腳本
支援批次模式執行受眾匯出
"""

import sys
import logging
from pathlib import Path
import functions_framework
from datetime import datetime, timedelta
from calendar import monthrange
project_root = Path(__file__).parent

from config_manager import ConfigManager
from client import BigQueryClient

# 設定日誌
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_previous_month_date_range():
    """
    計算上個月的開始和結束日期
    
    Returns:
        tuple: (start_date, end_date) 格式為 YYYYMMDD
    """
    # 獲取當前日期
    current_date = datetime.now()
    
    # 計算上個月的年份和月份
    if current_date.month == 1:
        # 如果是1月，上個月是去年的12月
        previous_year = current_date.year - 1
        previous_month = 12
    else:
        # 否則就是當年的上個月
        previous_year = current_date.year
        previous_month = current_date.month - 1
    
    # 獲取上個月的天數
    _, last_day = monthrange(previous_year, previous_month)
    
    # 格式化日期
    start_date = f"{previous_year:04d}{previous_month:02d}01"
    end_date = f"{previous_year:04d}{previous_month:02d}{last_day:02d}"
    
    logger.info(f"計算上個月日期範圍: {start_date} 到 {end_date}")
    return start_date, end_date


@functions_framework.http
def main(request):
    """主要執行函數 (Cloud Run 觸發)"""
    try:
        # 載入配置
        logger.info("載入配置...")
        config_manager = ConfigManager(config_dir=project_root / "config")

        # 覆蓋配置中的參數
        config = config_manager.get_user_config()

        # 設定 start_date 與 end_date - 自動計算上個月的完整日期範圍
        start_date, end_date = get_previous_month_date_range()
        config['analysis']['start_date'] = start_date
        config['analysis']['end_date'] = end_date
        
        logger.info(f"📅 本次執行將處理上個月的完整資料: {start_date} 到 {end_date}")

        # output_dir 直接用 config 預設值
        # 若有需要可在 config.yaml 設定 export.output_dir

        # 決定要處理的 segments
        # 預設同步 Google Spreadsheet 受眾映射
        logger.info("同步 Google Spreadsheet 受眾映射...")
        spreadsheet_config = config.get('spreadsheet', {})
        success = config_manager.json_config.sync_audiences_from_spreadsheet(
            spreadsheet_id=spreadsheet_config.get('audience_spreadsheet_id'),
            sheet_gid=spreadsheet_config.get('audience_sheet_gid')
        )
        if not success:
            logger.warning("受眾映射同步失敗，將使用 segment_id 作為名稱")

        # 決定 target_segments
        target_segments = list(config_manager.json_config.audience_mapping.keys())
        if not target_segments:
            logger.error("沒有指定要處理的受眾 segments")
            return '沒有指定要處理的受眾 segments', 400

        logger.info(f"將處理 {len(target_segments)} 個受眾")

        # 初始化 BigQuery 客戶端
        logger.info("初始化 BigQuery 客戶端...")

        # 合併 BigQuery 配置，包含 analysis 中的 lta_table_type
        bigquery_config = config_manager.get_bigquery_config().copy()
        analysis_config = config_manager.get_analysis_config()

        # 添加認證路徑
        gcp_auth_config = config_manager.get_gcp_config().get('auth', {})
        bigquery_config['credentials_path'] = gcp_auth_config.get('credentials_path')

        # 添加 LTA 表格類型
        bigquery_config['lta_table_type'] = analysis_config.get('lta_table_type', 'standard')

        bq_client = BigQueryClient(bigquery_config, config_manager=config_manager)

        # 決定要處理的 Data Partners
        # 預設用 config.yaml 的 target_data_partner_ids
        target_partner_ids = config.get('target_data_partner_ids', [])
        if not target_partner_ids:
            logger.error("沒有指定 target_data_partner_ids，請在 config.yaml 中設定")
            return '沒有指定 target_data_partner_ids，請在 config.yaml 中設定', 400

        partners = []
        for partner_id in target_partner_ids:
            partner = config_manager.json_config.get_data_partner_by_id(partner_id)
            if partner:
                partners.append(partner)
            else:
                logger.warning(f"找不到 Partner ID: {partner_id}")

        if not partners:
            logger.error("沒有找到任何有效的 Data Partners")
            return '沒有找到任何有效的 Data Partners', 400

        logger.info(f"將處理 {len(partners)} 個 Data Partners")

        all_partner_summaries = []
        failed_partners = []
        # 處理每個 Partner
        for partner in partners:
            partner_id = partner['id']
            partner_name = partner['name']

            logger.info(f"=== 處理 Partner: {partner_name} (ID: {partner_id}) ===")

            # 獲取 Partner 表格資訊
            table_info = config_manager.json_config.get_partner_table_info(partner_id)
            if not table_info:
                logger.error(f"無法獲取 Partner {partner_id} 的表格資訊")
                failed_partners.append(f"{partner_name} (缺少表格資訊)")
                continue

            try:
                # 執行查詢
                start_date = config['analysis']['start_date']
                end_date = config['analysis']['end_date']

                # 執行實際查詢
                summary = bq_client.get_audience_mapping_data(
                    segment_ids=target_segments,
                    partner_id=partner_id,
                    partner_name=partner_name,
                    start_date=start_date,
                    end_date=end_date
                )

                if summary:
                    all_partner_summaries.append(summary)
                else:
                    logger.error(f"Partner {partner_name} 處理失敗")
                    failed_partners.append(partner_name)
                
                logger.info(f"{'='*30} Partner: {partner_name} 處理結束 {'='*30}")

            except ValueError as e:
                if "查詢費用超過上限" in str(e):
                    logger.error(f"處理 Partner {partner_name} 時費用超過上限: {str(e)}")
                    return f'Partner {partner_name} 處理失敗：費用超過上限', 500
                else:
                    logger.error(f"處理 Partner {partner_name} 時發生數值錯誤: {str(e)}")
                    failed_partners.append(f"{partner_name} (數值錯誤: {str(e)})")
                    continue
            except Exception as e:
                logger.error(f"處理 Partner {partner_name} 時發生嚴重錯誤: {str(e)}")
                failed_partners.append(f"{partner_name} (嚴重錯誤: {str(e)})")
                continue

        logger.info("="*80)
        logger.info(f"📊 月度排程執行完畢 - {start_date} 到 {end_date} 最終彙總報告")
        logger.info("="*80)

        # --- 各別 Partner 明細 ---
        logger.info("【各別合作夥伴成效】")
        if not all_partner_summaries:
            logger.info("本次執行沒有成功處理任何合作夥伴。")
        else:
            for partner_summary in all_partner_summaries:
                duration_sec = partner_summary['duration_seconds']
                duration_min = duration_sec / 60
                cost = partner_summary['cost_usd']
                logger.info(f"  - {partner_summary['partner_name']}:")
                logger.info(f"    - 總耗時: {duration_sec:.2f} 秒 ({duration_min:.2f} 分鐘)")
                logger.info(f"    - 預估費用: ${cost:.6f} USD")

        # --- 失敗的 Partner ---
        if failed_partners:
            logger.info("【失敗的合作夥伴】")
            for failed_partner in failed_partners:
                logger.error(f"  - {failed_partner}")

        # --- 總計 ---
        logger.info("【總計】")
        
        # 計算總耗時
        total_duration_seconds = sum(s['duration_seconds'] for s in all_partner_summaries)
        total_duration_minutes = total_duration_seconds / 60

        # 直接從 bq_client 取得最準確的總費用資訊
        final_cost_summary = bq_client.get_cost_summary()
        
        logger.info(f"🕒 所有合作夥伴總耗時: {total_duration_seconds:.2f} 秒 ({total_duration_minutes:.2f} 分鐘)")
        logger.info(f"💰 BigQuery 總費用: ${final_cost_summary['total_cost_usd']:.6f} USD")
        logger.info(f"  - 總查詢次數: {final_cost_summary['query_count']}")
        
        if final_cost_summary['cost_by_type']:
            logger.info(f"  - 總費用明細:")
            for query_type, cost in final_cost_summary['cost_by_type'].items():
                logger.info(f"    - {query_type}: ${cost:.6f} USD")
        
        logger.info("="*80)
        
        # 根據執行結果決定返回狀態
        if failed_partners:
            if not all_partner_summaries:
                # 全部失敗
                logger.error(f"❌ 月度排程執行失敗！所有 Partner 都處理失敗")
                return f'Monthly scheduled export process failed for {start_date} to {end_date}. All partners failed: {", ".join(failed_partners)}', 500
            else:
                # 部分失敗
                logger.warning(f"⚠️ 月度排程執行部分成功！已處理 {start_date} 到 {end_date} 的資料，但有部分 Partner 失敗")
                return f'Monthly scheduled export process partially completed for {start_date} to {end_date}. Failed partners: {", ".join(failed_partners)}', 207  # 207 Multi-Status
        else:
            # 全部成功
            logger.info(f"✅ 月度排程執行完成！已處理 {start_date} 到 {end_date} 的資料")
            return f'Monthly scheduled export process completed successfully for {start_date} to {end_date}.', 200

    except KeyboardInterrupt:
        logger.info("\n⏹️  使用者中斷執行")
        return 'User interrupted execution', 200
    except ValueError as e:
        if "查詢費用超過上限" in str(e):
            logger.error(f"執行失敗：費用超過上限: {str(e)}")
            return f'執行失敗：費用超過上限', 500
        else:
            logger.error(f"執行失敗：數值錯誤: {str(e)}")
            return f'執行失敗：{str(e)}', 500
    except Exception as e:
        logger.error(f"執行失敗: {str(e)}")
        return f'執行失敗：{str(e)}', 500


if __name__ == "__main__":
    main(None)