"""
統一配置管理器
整合 YAML 使用者配置和 JSON 名單配置
"""

import os
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

from yaml_config import YamlConfig
from json_config import JsonConfig

logger = logging.getLogger(__name__)


class ConfigManager:
    """統一配置管理器"""
    
    def __init__(self, config_dir: str = "config"):
        """初始化配置管理器
        
        Args:
            config_dir: 配置文件目錄
        """
        self.config_dir = Path(config_dir)
        
        # 初始化各類配置
        self.yaml_config = YamlConfig(self.config_dir / "config.yaml")
        self.json_config = JsonConfig(self.config_dir)
        
        logger.info(f"配置管理器初始化完成，配置目錄：{self.config_dir}")
    
    # YAML 配置相關方法 (使用者設定)
    def get_user_config(self) -> Dict[str, Any]:
        """獲取使用者配置"""
        return self.yaml_config.get_config()
    
    def get_gcp_config(self) -> Dict[str, Any]:
        """獲取 GCP 配置"""
        return self.yaml_config.get_gcp_config()
    
    def get_bigquery_config(self) -> Dict[str, Any]:
        """獲取 BigQuery 配置"""
        return self.yaml_config.get_bigquery_config()
    
    def get_export_config(self) -> Dict[str, Any]:
        """獲取匯出配置"""
        return self.yaml_config.get_export_config()
    
    def get_analysis_config(self) -> Dict[str, Any]:
        """獲取分析配置"""
        return self.yaml_config.get_analysis_config()
    
    def get_cache_config(self) -> Dict[str, Any]:
        """獲取快取配置"""
        return self.yaml_config.get_cache_config()
    
    # JSON 配置相關方法 (名單管理)
    def get_all_data_partners(self) -> List[Dict]:
        """獲取所有 Data Partner"""
        return self.json_config.get_all_data_partners()
    
    def get_active_data_partners(self) -> List[Dict]:
        """獲取啟用的 Data Partner"""
        return self.json_config.get_active_data_partners()
    
    def get_data_partner_by_id(self, partner_id: str) -> Optional[Dict]:
        """根據 ID 獲取 Data Partner"""
        return self.json_config.get_data_partner_by_id(partner_id)
    
    def get_partner_table_info(self, partner_id: str) -> Optional[Dict]:
        """獲取 Partner 表格資訊"""
        return self.json_config.get_partner_table_info(partner_id)
    
    def get_all_audiences(self) -> List[Dict]:
        """獲取所有受眾"""
        return self.json_config.get_all_audiences()
    
    def get_enabled_audiences(self) -> List[Dict]:
        """獲取啟用的受眾"""
        return self.json_config.get_enabled_audiences()
    
    def get_audience_by_segment_id(self, segment_id: str) -> Optional[Dict]:
        """根據 segment_id 獲取受眾"""
        return self.json_config.get_audience_by_segment_id(segment_id)
    
    def validate_segment_ids(self, segment_ids: List[str]) -> Dict[str, bool]:
        """驗證 segment_id 是否存在"""
        return self.json_config.validate_segment_ids(segment_ids)
    
    # 配置驗證和更新
    def validate_config(self) -> Dict[str, Any]:
        """驗證所有配置"""
        validation_result = {
            'yaml_config': self.yaml_config.validate(),
            'json_config': self.json_config.validate(),
            'is_valid': True,
            'errors': []
        }
        
        # 檢查是否有錯誤
        if not validation_result['yaml_config']['is_valid']:
            validation_result['is_valid'] = False
            validation_result['errors'].extend(validation_result['yaml_config']['errors'])
        
        if not validation_result['json_config']['is_valid']:
            validation_result['is_valid'] = False
            validation_result['errors'].extend(validation_result['json_config']['errors'])
        
        return validation_result
    
    def reload_config(self):
        """重新載入所有配置"""
        logger.info("重新載入配置文件...")
        self.yaml_config.reload()
        self.json_config.reload()
        logger.info("配置文件重新載入完成")
    
    def sync_audiences_from_spreadsheet(self, 
                                      spreadsheet_id: Optional[str] = None,
                                      sheet_gid: Optional[str] = None) -> bool:
        """從 Google Spreadsheet 同步受眾配置"""
        return self.json_config.sync_audiences_from_spreadsheet(spreadsheet_id, sheet_gid)
    
    # 便利方法
    def get_credentials_path(self) -> Optional[str]:
        """獲取認證文件路徑"""
        gcp_config = self.get_gcp_config()
        return gcp_config.get('auth', {}).get('credentials_path')
    
    def get_output_dir(self) -> Path:
        """獲取輸出目錄"""
        export_config = self.get_export_config()
        return Path(export_config.get('output_dir', 'output'))
    
    def get_date_range(self) -> tuple:
        """獲取分析日期範圍"""
        analysis_config = self.get_analysis_config()
        return analysis_config.get('start_date'), analysis_config.get('end_date')
    
    def should_sync_to_gcs(self) -> bool:
        """是否同步到 Google Cloud Storage"""
        export_config = self.get_export_config()
        return export_config.get('sync_to_gcs', False)
    
    def should_sync_to_drive(self) -> bool:
        """是否同步到 Google Drive"""
        export_config = self.get_export_config()
        return export_config.get('sync_to_drive', True)